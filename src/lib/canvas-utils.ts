export const extractHTMLFromMessage = (content: string): string => {
  // Extract code blocks
  const codeBlockRegex = /```html\n([\s\S]*?)\n```/g
  const match = codeBlockRegex.exec(content)
  if (match) return match[1]

  // Extract inline HTML
  const htmlRegex = /<html[\s\S]*<\/html>/i
  const htmlMatch = htmlRegex.exec(content)
  if (htmlMatch) return htmlMatch[0]

  // Check for HTML-like patterns
  const htmlPatterns = [
    /<\!DOCTYPE\s+html/i,
    /<html[\s>]/i,
    /<head[\s>]/i,
    /<body[\s>]/i,
    /<div[\s>]/i,
    /<script[\s>]/i,
    /<style[\s>]/i
  ]

  const hasHTMLPattern = htmlPatterns.some(pattern => pattern.test(content))
  if (hasHTMLPattern) {
    // Try to extract the HTML portion from the content
    const lines = content.split('\n')
    const htmlStart = lines.findIndex(line => 
      /<\!DOCTYPE|<html|<head|<body/i.test(line)
    )
    const htmlEnd = lines.findIndex((line, index) => 
      index > htmlStart && /<\/html>/i.test(line)
    )
    
    if (htmlStart !== -1 && htmlEnd !== -1) {
      return lines.slice(htmlStart, htmlEnd + 1).join('\n')
    } else if (htmlStart !== -1) {
      return lines.slice(htmlStart).join('\n')
    }
    
    // Return the content as-is if it contains HTML-like patterns
    return content
  }

  // Default template
  return `<!DOCTYPE html>
<html>
<head>
  <title>New Canvas</title>
  <style>
    body { 
      font-family: Arial, sans-serif; 
      margin: 40px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
    .container {
      text-align: center;
      background: rgba(255, 255, 255, 0.1);
      padding: 40px;
      border-radius: 20px;
      backdrop-filter: blur(10px);
    }
    h1 {
      font-size: 3em;
      margin-bottom: 20px;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }
    button {
      background: #4CAF50;
      color: white;
      padding: 15px 30px;
      border: none;
      border-radius: 25px;
      font-size: 18px;
      cursor: pointer;
      margin: 10px;
      transition: all 0.3s ease;
    }
    button:hover {
      background: #45a049;
      transform: translateY(-2px);
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🎨 HTML Canvas Ready!</h1>
    <p>Start coding your amazing web creation here!</p>
    <button onclick="changeColor()">Change Background</button>
  </div>
  
  <script>
    console.log('Canvas ready!')
    
    function changeColor() {
      const colors = [
        'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
      ]
      const randomColor = colors[Math.floor(Math.random() * colors.length)]
      document.body.style.background = randomColor
    }
  </script>
</body>
</html>`
}

export const detectHTMLContent = (content: string): boolean => {
  const htmlPatterns = [
    /<html/i, /<!doctype/i, /<head>/i, /<body>/i,
    /<div.*>/i, /<script.*>/i, /<style.*>/i,
    /```html/i
  ]
  return htmlPatterns.some(pattern => pattern.test(content))
}

export const validateHTML = (html: string): boolean => {
  try {
    if (typeof DOMParser !== 'undefined') {
      const parser = new DOMParser()
      const doc = parser.parseFromString(html, 'text/html')
      return !doc.querySelector('parsererror')
    }
    // Server-side or when DOMParser is not available
    return html.length > 0 && html.includes('<')
  } catch {
    return false
  }
}

export const saveArtifact = async (data: { content: string; type?: string; title?: string }) => {
  const response = await fetch('/api/artifacts', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  })

  if (!response.ok) {
    throw new Error('Failed to save artifact')
  }

  return response.json()
}