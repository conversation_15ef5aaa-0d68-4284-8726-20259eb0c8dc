/**
 * Model Selection Module
 * Handles manual and automatic model selection with plan-based access control
 */

import { IntelligentRouter } from '@/lib/ai/router/router'
import { getDefaultModel } from '@/lib/ai/models/integrated-registry'
import { ChatRequest } from '../request/types'
import { modelAccessControl } from './access-control'
import { UserPlan } from '@/types'

const intelligentRouter = new IntelligentRouter()

export interface ModelSelection {
  model: string
  isManual: boolean
  routerReasoning?: string
  category?: string
  complexity?: string
  analysisResult?: any // Full analysis result for sophisticated system prompts
  wasRestricted?: boolean // Indicates if model was replaced due to plan restrictions
  restrictionMessage?: string
  requiresUpgrade?: boolean
  suggestedPlan?: UserPlan
}

/**
 * Select the appropriate AI model based on request and user preferences
 */
export async function selectModel(
  request: ChatRequest,
  session: any,
  anonymousSession?: any
): Promise<ModelSelection> {
  // Determine user plan
  const userPlan = session?.user?.plan || UserPlan.FREE
  const isAnonymous = !session?.user && !!anonymousSession
  
  // Manual model selection takes precedence - but check access
  if (request.manualModel && request.manualModel !== 'auto') {
    console.log('[Model Selector] Manual model selected:', request.manualModel)
    
    // Validate model access based on user plan
    const validationResult = await modelAccessControl.validateAndReplaceModel(
      request.manualModel,
      userPlan,
      isAnonymous
    )
    
    try {
      // Still do intelligent analysis for sophisticated system prompts
      const lastUserMessage = request.messages.findLast(m => m.role === 'user')
      const userMessage = lastUserMessage?.content || ''
      
      // Prepare router input for analysis - convert content to string if needed
      const queryString = typeof userMessage === 'string' ? userMessage : 
        Array.isArray(userMessage) ? userMessage.map(part => 
          typeof part === 'string' ? part : part.text || ''
        ).join(' ') : ''
      
      const routerInput = {
        query: queryString,
        conversationLength: request.messages.length,
        hasCode: queryString.includes('```') || queryString.toLowerCase().includes('code'),
        userPlan: userPlan || 'FREE',
        userId: session?.user?.id,
        webSearchEnabled: request.webSearchEnabled,
      }
      
      console.log('[Model Selector] Analyzing message for manual selection intelligence...')
      // Use the router's analyze method instead of non-existent analyzePrompt
      const routerResult = await intelligentRouter.route(routerInput)
      const analysisResult = routerResult.analysisResult || {}
      
      return { 
        model: validationResult.model,
        isManual: true,
        category: analysisResult.category,
        complexity: analysisResult.complexity,
        analysisResult: analysisResult,
        routerReasoning: validationResult.wasReplaced ?
          `Model ${request.manualModel} restricted for ${userPlan} plan. Using ${validationResult.model} instead.` :
          `Manual selection: ${request.manualModel} with intelligent analysis (${analysisResult.category || 'unknown'}/${analysisResult.complexity || 'unknown'})`,
        wasRestricted: validationResult.wasReplaced,
        restrictionMessage: validationResult.message,
        requiresUpgrade: validationResult.requiresUpgrade,
        suggestedPlan: validationResult.suggestedPlan
      }
    } catch (error) {
      console.warn('[Model Selector] Analysis failed for manual selection, using basic mode:', error)
      return { 
        model: validationResult.model, 
        isManual: true,
        routerReasoning: validationResult.wasReplaced ?
          `Model ${request.manualModel} restricted for ${userPlan} plan. Using ${validationResult.model} instead.` :
          `Manual selection: ${request.manualModel} (analysis unavailable)`,
        wasRestricted: validationResult.wasReplaced,
        restrictionMessage: validationResult.message,
        requiresUpgrade: validationResult.requiresUpgrade,
        suggestedPlan: validationResult.suggestedPlan
      }
    }
  }
  
  // Use intelligent router for automatic selection
  try {
    const lastUserMessage = request.messages.findLast(m => m.role === 'user')
    const userMessage = lastUserMessage?.content || ''
    
    // Convert content to string if needed
    const queryString = typeof userMessage === 'string' ? userMessage : 
      Array.isArray(userMessage) ? userMessage.map(part => 
        typeof part === 'string' ? part : part.text || ''
      ).join(' ') : ''
    
    // Prepare router input
    const routerInput = {
      query: queryString,
      conversationLength: request.messages.length,
      hasCode: queryString.includes('```') || queryString.toLowerCase().includes('code'),
      userPlan: userPlan || 'FREE',
      userId: session?.user?.id,
      webSearchEnabled: request.webSearchEnabled,
    }
    
    console.log('[Model Selector] Calling intelligent router...')
    const routerResult = await intelligentRouter.route(routerInput)
    
    // Get default model if router didn't select one
    const selectedModel = routerResult.selectedModel || 
      (await getDefaultModel(userPlan))?.id || 'openai/gpt-4o-mini'
    
    // Validate router's selection based on user plan
    const validationResult = await modelAccessControl.validateAndReplaceModel(
      selectedModel,
      userPlan,
      isAnonymous
    )
    
    return { 
      model: validationResult.model, 
      isManual: false,
      routerReasoning: validationResult.wasReplaced ?
        `${routerResult.reasoning || ''}\n[Access Control] Model ${routerResult.selectedModel} restricted for ${userPlan} plan. Using ${validationResult.model} instead.` :
        (routerResult.reasoning || 'Automatic model selection'),
      category: routerResult.analysisResult?.category,
      complexity: routerResult.analysisResult?.complexity,
      analysisResult: routerResult.analysisResult,
      wasRestricted: validationResult.wasReplaced,
      restrictionMessage: validationResult.message,
      requiresUpgrade: validationResult.requiresUpgrade,
      suggestedPlan: validationResult.suggestedPlan
    }
  } catch (error) {
    console.error('[Model Selector] Router failed, using default:', error)
    const defaultModel = await getDefaultModel(userPlan)
    const defaultModelId = defaultModel?.id || 'openai/gpt-4o-mini'
    
    // Even default model should be validated
    const validationResult = await modelAccessControl.validateAndReplaceModel(
      defaultModelId,
      userPlan,
      isAnonymous
    )
    
    return { 
      model: validationResult.model, 
      isManual: false,
      wasRestricted: validationResult.wasReplaced,
      restrictionMessage: validationResult.message,
      requiresUpgrade: validationResult.requiresUpgrade,
      suggestedPlan: validationResult.suggestedPlan
    }
  }
}

/**
 * Check if a model supports web search natively
 */
export function supportsNativeWebSearch(model: string): boolean {
  // Perplexity models have native web search
  return model.includes('perplexity/') && model.includes('online')
}

/**
 * Check if a model supports reasoning/thinking
 */
export function supportsReasoning(model: string): boolean {
  const reasoningModels = [
    'o1', 'o1-mini', 'o1-preview',
    'o3', 'o3-mini',
    'claude-3-5-sonnet-20241022',
    'deepseek-r1',
    'deepthink',
  ]
  
  return reasoningModels.some(rm => model.includes(rm))
}

/**
 * Get model configuration (temperature, max tokens, etc.)
 */
export function getModelConfig(model: string, request: ChatRequest) {
  const baseConfig = {
    temperature: 0.7,
    maxOutputTokens: 4000,
  }
  
  // Reasoning models often work better with lower temperature
  if (supportsReasoning(model)) {
    baseConfig.temperature = 0.3
  }
  
  // Ultra think mode gets more tokens
  if (request.ultraThink) {
    baseConfig.maxOutputTokens = 8000
  }
  
  return baseConfig
}