/**
 * Citation Handler for AI SDK v5
 * 
 * Handles extraction of search results from tool calls and 
 * linking them to citation numbers in the AI response
 */

import { Tool<PERSON>allPart, ToolResultPart } from 'ai';

export interface CitationSource {
  title: string;
  url: string;
  snippet?: string;
  domain?: string;
}

/**
 * Extract search results from tool calls and results
 * Maps them to citation numbers based on order
 */
export function extractSearchResultsFromToolCalls(
  toolCalls?: ToolCallPart[],
  toolResults?: ToolResultPart[]
): CitationSource[] {
  if (!toolCalls || !toolResults) return [];
  
  const searchResults: CitationSource[] = [];
  
  // Find all search-related tool calls and their results
  toolCalls.forEach((toolCall, index) => {
    // Support multiple search tool names
    const isSearchTool = toolCall.toolName === 'searchWeb' || 
                        toolCall.toolName.toLowerCase().includes('search') ||
                        toolCall.toolName === 'perplexitySearch' ||
                        toolCall.toolName === 'braveSearch' ||
                        toolCall.toolName === 'webSearch' ||
                        toolCall.toolName === 'firecrawlSearch';
                        
    if (isSearchTool) {
      // Find the corresponding tool result
      const result = toolResults.find(r => r.toolCallId === toolCall.toolCallId);
      
      if (result?.result && typeof result.result === 'object') {
        const searchData = result.result as any;
        
        // Handle different result structures from various search tools
        let resultsArray = null;
        
        if (Array.isArray(searchData.results)) {
          resultsArray = searchData.results;
        } else if (Array.isArray(searchData.sources)) {
          resultsArray = searchData.sources;
        } else if (Array.isArray(searchData)) {
          resultsArray = searchData;
        }
        
        if (resultsArray) {
          resultsArray.forEach((searchResult: any) => {
            // Handle different URL field names
            const url = searchResult.url || searchResult.link || searchResult.href || '';
            
            if (url) {
              // Extract domain from URL
              let domain = '';
              try {
                domain = new URL(url).hostname.replace('www.', '');
              } catch {
                domain = 'source';
              }
              
              searchResults.push({
                title: searchResult.title || 'Untitled',
                url: url,
                snippet: searchResult.snippet || searchResult.description || searchResult.summary || '',
                domain
              });
            }
          });
        }
      }
    }
  });
  
  return searchResults;
}

/**
 * Process AI response text to make citations interactive
 * Replaces [1], [2], etc. with React components
 */
export function processCitationsInText(
  text: string,
  sources: CitationSource[]
): Array<string | { type: string; number: number; source: CitationSource }> {
  if (!sources.length) return [text];
  
  // Split text by citation pattern [1], [2], etc.
  const parts = text.split(/(\[\d+\])/g);
  
  return parts.map((part, index) => {
    const citationMatch = part.match(/^\[(\d+)\]$/);
    if (citationMatch) {
      const citationNumber = parseInt(citationMatch[1]);
      const source = sources[citationNumber - 1]; // 0-indexed
      
      if (source) {
        // Return citation component data
        return {
          type: 'citation',
          number: citationNumber,
          source
        };
      }
    }
    
    // Return plain text string
    return part;
  });
}

/**
 * Extract sources from Google's native search grounding
 * Google Gemini models return search results differently
 */
export function extractGoogleSearchSources(
  modelResponse: any
): CitationSource[] {
  const sources: CitationSource[] = [];
  
  // Check for Google's grounding metadata
  if (modelResponse?.groundingMetadata?.searchEntryPoint?.groundingChunks) {
    const chunks = modelResponse.groundingMetadata.searchEntryPoint.groundingChunks;
    
    chunks.forEach((chunk: any) => {
      if (chunk.web) {
        sources.push({
          title: chunk.web.title || 'Google Search Result',
          url: chunk.web.uri || chunk.web.url || '',
          snippet: chunk.web.snippet || chunk.chunk?.text || '',
          domain: chunk.web.site || ''
        });
      }
    });
  }
  
  return sources;
}

/**
 * Main handler to process message and extract all citation sources
 */
export function processCitationsForMessage(
  message: any,
  toolCalls?: ToolCallPart[],
  toolResults?: ToolResultPart[]
): {
  sources: CitationSource[];
  processedContent: Array<string | { type: string; number: number; source: CitationSource }>;
} {
  let sources: CitationSource[] = [];
  
  // 1. Check for tool-based search results (our searchWeb tool)
  const toolSources = extractSearchResultsFromToolCalls(toolCalls, toolResults);
  if (toolSources.length > 0) {
    sources = toolSources;
  }
  
  // 2. Check for Google's native search grounding
  if (message.metadata?.googleGrounding) {
    const googleSources = extractGoogleSearchSources(message.metadata.googleGrounding);
    sources = [...sources, ...googleSources];
  }
  
  // 3. Check if sources are already in metadata (backward compatibility)
  if (message.metadata?.searchResults && sources.length === 0) {
    sources = message.metadata.searchResults.map((result: any) => ({
      title: result.title,
      url: result.url || result.link,
      snippet: result.snippet,
      domain: result.domain || new URL(result.url || result.link).hostname.replace('www.', '')
    }));
  }
  
  // Process the content to make citations interactive
  const processedContent = processCitationsInText(message.content || '', sources);
  
  return {
    sources,
    processedContent
  };
}