/**
 * Research Supervisor - Agent Coordination
 * 
 * @description
 * Coordinates multiple research agents, manages task distribution,
 * and synthesizes findings into coherent analysis. Implements the
 * supervisor pattern for multi-agent orchestration.
 * 
 * @module lib/ai/deep-research/supervisor
 */

import { streamText, generateText, tool } from 'ai';
import { z } from 'zod';
import {
  AgentType,
  AgentStatus,
  AgentTaskType,
  ResearchPhase
} from './types';
import type {
  DeepResearchOptions,
  IResearchAgent,
  AgentTask,
  TaskPriority,
  DeepResearchConfig,
  AgentTaskResult
} from './types';
import { deepResearchConfig } from './config';
import { BaseAgent } from './agents/base';
import { ResearchAgent as ResearchAgentImpl } from './agents/research';
import { AnalysisAgent } from './agents/analysis';
import { FactCheckAgent } from './agents/fact-check';
import { CompressionAgent } from './agents/compression';
import { ReportAgent } from './agents/report';
import { ModelSelector } from './utils/model-selection';
import { routerLogger } from '@/lib/logger';
import { v4 as uuidv4 } from 'uuid';

/**
 * Research plan structure
 */
interface ResearchPlan {
  query: string;
  objectives: string[];
  scope: {
    topics: string[];
    depth: 'surface' | 'moderate' | 'deep';
    breadth: 'narrow' | 'balanced' | 'comprehensive';
  };
  agentAllocation: {
    research: number;
    analysis: number;
    factCheck: number;
    compression: number;
  };
  timeline: {
    maxDuration: number;
    phases: {
      research: number;
      analysis: number;
      synthesis: number;
    };
  };
  searchStrategies: string[];
  qualityThresholds: {
    minSources: number;
    minConfidence: number;
    requiredVerification: boolean;
  };
}

/**
 * Research Supervisor
 * 
 * Manages the coordination of multiple research agents,
 * task distribution, and synthesis of findings.
 */
export class ResearchSupervisor {
  private config = deepResearchConfig.getConfig();
  private modelSelector: ModelSelector;
  private logger = routerLogger;
  private agents: Map<string, BaseAgent> = new Map();
  private tasks: Map<string, AgentTask> = new Map();
  
  constructor() {
    this.modelSelector = new ModelSelector();
  }

  /**
   * Plan research strategy based on query complexity
   */
  async planResearch(
    query: string,
    options?: DeepResearchOptions
  ): Promise<ResearchPlan> {
    const model = await this.modelSelector.selectModel('research');
    
    const planningTool = tool({
      description: 'Create a comprehensive research plan',
      parameters: z.object({
        objectives: z.array(z.string()).describe('Key research objectives'),
        topics: z.array(z.string()).describe('Main topics to investigate'),
        depth: z.enum(['surface', 'moderate', 'deep']).describe('Research depth required'),
        breadth: z.enum(['narrow', 'balanced', 'comprehensive']).describe('Research breadth'),
        researchAgents: z.number().min(1).max(10).describe('Number of research agents needed'),
        analysisAgents: z.number().min(1).max(5).describe('Number of analysis agents needed'),
        requiresFactChecking: z.boolean().describe('Whether fact checking is critical'),
        estimatedDuration: z.number().describe('Estimated time in seconds'),
        searchStrategies: z.array(z.string()).describe('Search strategies to employ'),
        minSourceCount: z.number().describe('Minimum sources required'),
        confidenceThreshold: z.number().min(0).max(1).describe('Minimum confidence score')
      }),
      execute: async (params) => params
    });

    // Use generateText with timeout for better reliability in supervisor context
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout
    
    let planData: any;
    try {
      const result = await generateText({
        model,
        messages: [
          {
            role: 'system',
            content: `You are a research planning supervisor. Analyze the query and create a comprehensive research plan.
            
Consider:
- Query complexity and scope
- Required research depth and breadth
- Optimal agent allocation
- Search strategies needed
- Quality thresholds
- Time constraints

Provide a detailed plan that maximizes research quality while respecting constraints.`
          },
          {
            role: 'user',
            content: `Create a research plan for: "${query}"

Options: ${JSON.stringify(options || {})}`
          }
        ],
        tools: { planningTool },
        toolChoice: { type: 'tool', toolName: 'planningTool' },
        maxOutputTokens: 2000,
        temperature: 0.3,
        abortSignal: controller.signal
      });
      
      clearTimeout(timeoutId);
      planData = result.toolCalls[0]?.args;
    } catch (error) {
      clearTimeout(timeoutId);
      this.logger.error('Research planning failed, using fallback plan', { error });
      
      // Fallback plan if AI call fails
      planData = {
        objectives: [`Research ${query}`, 'Analyze findings', 'Generate comprehensive report'],
        topics: [query],
        depth: 'moderate',
        breadth: 'balanced',
        researchAgents: 2,
        analysisAgents: 1,
        requiresFactChecking: true,
        estimatedDuration: 180,
        searchStrategies: ['web search', 'analysis', 'synthesis'],
        minSourceCount: 5,
        confidenceThreshold: 0.7
      };
    }
    if (!planData) {
      throw new Error('Failed to generate research plan');
    }

    return {
      query,
      objectives: planData.objectives,
      scope: {
        topics: planData.topics,
        depth: planData.depth,
        breadth: planData.breadth
      },
      agentAllocation: {
        research: planData.researchAgents,
        analysis: planData.analysisAgents,
        factCheck: planData.requiresFactChecking ? 1 : 0,
        compression: 1
      },
      timeline: {
        maxDuration: planData.estimatedDuration,
        phases: {
          research: planData.estimatedDuration * 0.5,
          analysis: planData.estimatedDuration * 0.3,
          synthesis: planData.estimatedDuration * 0.2
        }
      },
      searchStrategies: planData.searchStrategies,
      qualityThresholds: {
        minSources: planData.minSourceCount,
        minConfidence: planData.confidenceThreshold,
        requiredVerification: planData.requiresFactChecking
      }
    };
  }

  /**
   * Spawn research agents based on plan
   */
  async spawnAgents(
    plan: ResearchPlan,
    config: DeepResearchConfig
  ): Promise<IResearchAgent[]> {
    const agents: IResearchAgent[] = [];
    
    // Spawn research agents
    for (let i = 0; i < plan.agentAllocation.research; i++) {
      const agent = await this.createAgent(
        AgentType.RESEARCH,
        `research-${i + 1}`,
        plan.objectives[i] || plan.query
      );
      agents.push(agent);
    }
    
    // Spawn analysis agents
    for (let i = 0; i < plan.agentAllocation.analysis; i++) {
      const agent = await this.createAgent(
        AgentType.ANALYSIS,
        `analysis-${i + 1}`,
        'Analyze and synthesize research findings'
      );
      agents.push(agent);
    }
    
    // Spawn fact check agent if needed
    if (plan.agentAllocation.factCheck > 0) {
      const agent = await this.createAgent(
        AgentType.FACT_CHECK,
        'fact-checker',
        'Verify facts and validate sources'
      );
      agents.push(agent);
    }
    
    // Always spawn compression agent
    const compressionAgent = await this.createAgent(
      AgentType.COMPRESSION,
      'compressor',
      'Summarize and compress findings'
    );
    agents.push(compressionAgent);
    
    return agents;
  }

  /**
   * Create a specific type of agent
   */
  private async createAgent(
    type: AgentType,
    name: string,
    task: string
  ): Promise<IResearchAgent> {
    const id = uuidv4();
    const model = await this.modelSelector.selectModel(type);
    
    let agentImpl: BaseAgent;
    
    switch (type) {
      case AgentType.RESEARCH:
        agentImpl = new ResearchAgentImpl(id, name, model);
        break;
      case AgentType.ANALYSIS:
        agentImpl = new AnalysisAgent(id, name, model);
        break;
      case AgentType.FACT_CHECK:
        agentImpl = new FactCheckAgent(id, name, model);
        break;
      case AgentType.COMPRESSION:
        agentImpl = new CompressionAgent(id, name, model);
        break;
      case AgentType.REPORT:
        agentImpl = new ReportAgent(id, name, model);
        break;
      default:
        throw new Error(`Unknown agent type: ${type}`);
    }
    
    this.agents.set(id, agentImpl);
    
    const agent: IResearchAgent = {
      id,
      type,
      name,
      description: task,
      status: AgentStatus.IDLE,
      model: model as any,
      capabilities: agentImpl.capabilities,
      metrics: {
        tasksCompleted: 0,
        tasksSuccessful: 0,
        tasksFailed: 0,
        averageProcessingTime: 0,
        averageTokenUsage: 0,
        reliabilityScore: 1.0,
        lastPerformanceUpdate: new Date()
      }
    };
    
    return agent;
  }

  /**
   * Analyze findings from all agents
   */
  async analyzeFindings(findings: any[]): Promise<any> {
    const model = await this.modelSelector.selectModel('analysis');
    
    const analysisTool = tool({
      description: 'Analyze and synthesize research findings',
      parameters: z.object({
        summary: z.string().describe('Executive summary of findings'),
        keyInsights: z.array(z.object({
          insight: z.string(),
          confidence: z.number().min(0).max(1),
          sources: z.array(z.string())
        })).describe('Key insights with confidence scores'),
        patterns: z.array(z.string()).describe('Patterns identified across sources'),
        contradictions: z.array(z.object({
          topic: z.string(),
          viewpoints: z.array(z.string())
        })).describe('Contradictory information found'),
        gaps: z.array(z.string()).describe('Information gaps identified'),
        recommendations: z.array(z.string()).describe('Recommendations based on findings'),
        overallConfidence: z.number().min(0).max(1).describe('Overall confidence in findings'),
        researchQuality: z.enum(['low', 'medium', 'high']).describe('Assessment of research quality')
      }),
      execute: async (params) => params
    });

    // Use generateText with timeout for analysis
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 45000); // 45 second timeout for analysis
    
    let analysis: any;
    try {
      const result = await generateText({
        model,
        messages: [
          {
            role: 'system',
            content: `You are a research analysis supervisor. Analyze the collected findings and provide comprehensive insights.

Your analysis should:
- Synthesize information from multiple sources
- Identify key patterns and insights
- Highlight contradictions or conflicts
- Assess overall research quality
- Provide actionable recommendations`
          },
          {
            role: 'user',
            content: `Analyze these research findings:\n\n${JSON.stringify(findings, null, 2)}`
          }
        ],
        tools: { analysisTool },
        toolChoice: { type: 'tool', toolName: 'analysisTool' },
        maxOutputTokens: 4000,
        temperature: 0.2,
        abortSignal: controller.signal
      });
      
      clearTimeout(timeoutId);
      analysis = result.toolCalls[0]?.args;
    } catch (error) {
      clearTimeout(timeoutId);
      this.logger.error('Analysis failed, using fallback', { error });
      
      // Fallback analysis
      analysis = {
        summary: 'Research analysis completed with limited processing due to technical constraints.',
        keyInsights: [{
          insight: 'Multiple sources were processed and analyzed',
          confidence: 0.7,
          sources: ['multiple']
        }],
        patterns: ['Data aggregated from various sources'],
        contradictions: [],
        gaps: ['Analysis may be incomplete due to processing limitations'],
        recommendations: ['Review findings manually for additional insights'],
        overallConfidence: 0.7,
        researchQuality: 'medium' as const
      };
    }
    if (!analysis) {
      throw new Error('Failed to analyze findings');
    }

    return {
      ...analysis,
      executiveSummary: analysis.summary,
      rawFindings: findings,
      timestamp: new Date()
    };
  }

  /**
   * Coordinate agent tasks and monitor progress
   */
  async coordinateTasks(
    agents: IResearchAgent[],
    tasks: AgentTask[]
  ): Promise<void> {
    // Distribute tasks to agents based on capabilities
    for (const task of tasks) {
      const suitableAgent = this.findSuitableAgent(agents, task);
      if (suitableAgent) {
        await this.assignTask(suitableAgent, task);
      } else {
        this.logger.warn('No suitable agent found for task', { task });
      }
    }
  }

  /**
   * Find the most suitable agent for a task
   */
  private findSuitableAgent(
    agents: IResearchAgent[],
    task: AgentTask
  ): IResearchAgent | null {
    // Find idle agents with matching capabilities
    const candidates = agents.filter(agent => {
      return agent.status === AgentStatus.IDLE &&
             this.agentCanHandleTask(agent, task);
    });
    
    // Sort by reliability score
    candidates.sort((a, b) => b.metrics.reliabilityScore - a.metrics.reliabilityScore);
    
    return candidates[0] || null;
  }

  /**
   * Check if agent can handle a specific task
   */
  private agentCanHandleTask(
    agent: IResearchAgent,
    task: AgentTask
  ): boolean {
    // Match agent type with task type
    const typeMatch: Record<AgentTaskType, AgentType[]> = {
      [AgentTaskType.SEARCH]: [AgentType.RESEARCH],
      [AgentTaskType.ANALYZE]: [AgentType.ANALYSIS, AgentType.RESEARCH],
      [AgentTaskType.FACT_CHECK]: [AgentType.FACT_CHECK],
      [AgentTaskType.SYNTHESIZE]: [AgentType.ANALYSIS, AgentType.COMPRESSION],
      [AgentTaskType.GENERATE_REPORT]: [AgentType.REPORT],
      [AgentTaskType.VALIDATE_SOURCES]: [AgentType.FACT_CHECK, AgentType.RESEARCH],
      [AgentTaskType.EXTRACT_DATA]: [AgentType.RESEARCH, AgentType.ANALYSIS]
    };
    
    return typeMatch[task.type]?.includes(agent.type) || false;
  }

  /**
   * Assign a task to an agent
   */
  private async assignTask(
    agent: IResearchAgent,
    task: AgentTask
  ): Promise<void> {
    agent.status = AgentStatus.ACTIVE;
    agent.currentTask = task;
    task.agentId = agent.id;
    task.status = AgentStatus.ACTIVE;
    task.startTime = new Date();
    
    this.tasks.set(task.id, task);
    
    this.logger.info('Task assigned to agent', {
      taskId: task.id,
      agentId: agent.id,
      taskType: task.type
    });
  }

  /**
   * Monitor agent performance and adapt strategy
   */
  async monitorAndAdapt(
    agents: IResearchAgent[]
  ): Promise<void> {
    for (const agent of agents) {
      if (agent.status === AgentStatus.FAILED) {
        // Reassign failed tasks
        if (agent.currentTask) {
          const newAgent = this.findSuitableAgent(
            agents.filter(a => a.id !== agent.id),
            agent.currentTask
          );
          if (newAgent) {
            await this.assignTask(newAgent, agent.currentTask);
          }
        }
      }
      
      // Update performance metrics
      if (agent.metrics.reliabilityScore < 0.5) {
        this.logger.warn('Agent performance degraded', {
          agentId: agent.id,
          reliability: agent.metrics.reliabilityScore
        });
      }
    }
  }

  /**
   * Generate final synthesis from all findings
   */
  async synthesizeResults(
    analysis: any,
    options?: DeepResearchOptions
  ): Promise<string> {
    const model = await this.modelSelector.selectModel('report');
    
    // Use generateText with timeout for synthesis
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 60000); // 1 minute timeout for synthesis
    
    try {
      const result = await generateText({
        model,
        messages: [
          {
            role: 'system',
            content: `You are a research synthesis expert. Create a comprehensive, well-structured synthesis of the research findings.`
          },
          {
            role: 'user',
            content: `Synthesize this research analysis into a coherent report:\n\n${JSON.stringify(analysis, null, 2)}`
          }
        ],
        maxOutputTokens: 6000,
        temperature: 0.3,
        abortSignal: controller.signal
      });
      
      clearTimeout(timeoutId);
      return result.text;
    } catch (error) {
      clearTimeout(timeoutId);
      this.logger.error('Synthesis failed', { error });
      return `# Research Synthesis\n\nSynthesis process encountered technical difficulties. The research findings suggest further investigation may be beneficial.`;
    }
  }
}