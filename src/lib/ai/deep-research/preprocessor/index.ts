import { generateText } from 'ai';
import { google } from '@ai-sdk/google';
import * as cheerio from 'cheerio';
import { routerLogger as preprocessorLogger } from '@/lib/logger';
import { TokenTracker } from '../types';
import type { SearchResult } from '../types';

// Retry configuration
const RETRY_CONFIG = {
  maxRetries: 3,
  backoffMs: [1000, 2000, 4000], // Exponential backoff
  timeout: 30000, // 30 seconds per attempt
};

// Model selection based on quality/cost tradeoff
const MODELS = {
  flash: {
    name: 'gemini-2.5-flash',
    costPer1M: { input: 0.075, output: 0.30 },
    maxOutputTokens: 3000,
  },
  flashLite: {
    name: 'gemini-2.5-flash-lite',
    costPer1M: { input: 0.10, output: 0.40 },
    maxOutputTokens: 2000,
  },
} as const;

interface PreprocessorConfig {
  model?: 'flash' | 'flashLite';
  maxOutputTokens?: number;
  targetTokensPerSource?: number;
  enableMarkdownConversion?: boolean;
  tokenTracker?: TokenTracker;
}

interface ProcessedResult {
  url: string;
  title: string;
  keyPoints: string;
  relevantSections: string;
  summary: string;
  metadata: {
    originalTokens: number;
    processedTokens: number;
    compressionRatio: number;
    model: string;
    processingTimeMs: number;
    retries: number;
  };
  markdown: string;
}

export class PreprocessorAgent {
  private config: Required<PreprocessorConfig>;
  private tokenTracker: TokenTracker;

  constructor(config: PreprocessorConfig = {}) {
    this.config = {
      model: config.model || 'flash',
      maxOutputTokens: config.maxOutputTokens || MODELS[config.model || 'flash'].maxOutputTokens,
      targetTokensPerSource: config.targetTokensPerSource || 2000,
      enableMarkdownConversion: config.enableMarkdownConversion ?? true,
      tokenTracker: config.tokenTracker || new TokenTracker(),
    };
    this.tokenTracker = this.config.tokenTracker;
  }

  async preprocessSearchResult(
    result: SearchResult,
    query: string,
    attempt = 0
  ): Promise<ProcessedResult> {
    const startTime = Date.now();
    const modelConfig = MODELS[this.config.model];
    
    try {
      // Convert HTML to clean markdown if needed
      const content = this.config.enableMarkdownConversion
        ? this.htmlToMarkdown(result.content || '')
        : (result.content || '');

      const originalTokens = this.estimateTokens(content);
      
      // Build the extraction prompt
      const prompt = this.buildExtractionPrompt(query, content);
      
      preprocessorLogger.info('Starting preprocessing', {
        url: result.url,
        originalTokens,
        model: modelConfig.name,
        attempt: attempt + 1,
      });

      // Call Gemini with retry logic
      const response = await this.callWithRetry(async () => {
        return await generateText({
          model: google(modelConfig.name),
          prompt,
          maxOutputTokens: this.config.maxOutputTokens,
          temperature: 1, // Required for consistency
          abortSignal: AbortSignal.timeout(RETRY_CONFIG.timeout),
        });
      }, attempt);

      // Track token usage
      if (response.usage) {
        // Token tracking simplified for compatibility
        console.log(`[TokenUsage] Model: ${modelConfig.name}, Tokens: ${response.usage.totalTokens || 0}`);
      }

      // Parse the structured response
      const processed = this.parseProcessedContent(response.text);
      const processedTokens = this.estimateTokens(
        processed.keyPoints + processed.relevantSections + processed.summary
      );

      const processingResult: ProcessedResult = {
        url: result.url,
        title: result.title || 'Untitled',
        keyPoints: processed.keyPoints,
        relevantSections: processed.relevantSections,
        summary: processed.summary,
        metadata: {
          originalTokens,
          processedTokens,
          compressionRatio: processedTokens / originalTokens,
          model: modelConfig.name,
          processingTimeMs: Date.now() - startTime,
          retries: attempt,
        },
        markdown: this.formatAsMarkdown(processed, result),
      };

      preprocessorLogger.info('Preprocessing complete', {
        url: result.url,
        compressionRatio: processingResult.metadata.compressionRatio.toFixed(2),
        processingTimeMs: processingResult.metadata.processingTimeMs,
        tokensSaved: originalTokens - processedTokens,
      });

      return processingResult;
    } catch (error) {
      if (attempt < RETRY_CONFIG.maxRetries - 1) {
        const backoffTime = RETRY_CONFIG.backoffMs[attempt];
        preprocessorLogger.warn('Preprocessing failed, retrying', {
          url: result.url,
          attempt: attempt + 1,
          backoffMs: backoffTime,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
        
        await this.sleep(backoffTime);
        return this.preprocessSearchResult(result, query, attempt + 1);
      }
      
      // Final failure - return minimal result
      preprocessorLogger.error('Preprocessing failed after all retries', {
        url: result.url,
        attempts: attempt + 1,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      return this.createFallbackResult(result, error);
    }
  }

  private htmlToMarkdown(html: string): string {
    try {
      const $ = cheerio.load(html);
      
      // Remove script and style tags
      $('script, style, noscript').remove();
      
      // Convert headers
      $('h1, h2, h3, h4, h5, h6').each((_, elem) => {
        const $elem = $(elem);
        const level = parseInt(elem.tagName.slice(1));
        const text = $elem.text().trim();
        $elem.replaceWith(`\n${'#'.repeat(level)} ${text}\n`);
      });
      
      // Convert paragraphs
      $('p').each((_, elem) => {
        const $elem = $(elem);
        const text = $elem.text().trim();
        if (text) $elem.replaceWith(`\n${text}\n`);
      });
      
      // Convert lists
      $('ul, ol').each((_, elem) => {
        const $elem = $(elem);
        const isOrdered = elem.tagName === 'ol';
        let index = 1;
        
        $elem.find('li').each((_, li) => {
          const $li = $(li);
          const text = $li.text().trim();
          const prefix = isOrdered ? `${index++}.` : '-';
          $li.replaceWith(`${prefix} ${text}\n`);
        });
      });
      
      // Convert links
      $('a').each((_, elem) => {
        const $elem = $(elem);
        const text = $elem.text().trim();
        const href = $elem.attr('href');
        if (text && href) {
          $elem.replaceWith(`[${text}](${href})`);
        }
      });
      
      // Convert code blocks
      $('pre code, code').each((_, elem) => {
        const $elem = $(elem);
        const text = $elem.text().trim();
        const isBlock = (elem as any).parentElement?.tagName === 'pre';
        if (isBlock) {
          $elem.replaceWith(`\n\`\`\`\n${text}\n\`\`\`\n`);
        } else {
          $elem.replaceWith(`\`${text}\``);
        }
      });
      
      // Get clean text and normalize whitespace
      let markdown = $.text();
      markdown = markdown.replace(/\n{3,}/g, '\n\n').trim();
      
      return markdown;
    } catch (error) {
      preprocessorLogger.warn('HTML to Markdown conversion failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      // Fallback to basic text extraction
      return html.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
    }
  }

  private buildExtractionPrompt(query: string, content: string): string {
    return `You are an expert research assistant preprocessing web content for deep analysis. Extract the most relevant information for the research query.

RESEARCH QUERY: "${query}"

EXTRACTION INSTRUCTIONS:
1. Identify and extract KEY POINTS directly relevant to the query (max 5-7 points)
2. Extract RELEVANT SECTIONS with important details, data, or evidence
3. Create a CONCISE SUMMARY of the overall relevance to the query

FORMAT YOUR RESPONSE EXACTLY AS:
---KEY POINTS---
- [Point 1]
- [Point 2]
...

---RELEVANT SECTIONS---
[Extracted relevant content with context]

---SUMMARY---
[Brief summary of relevance and key findings]

IMPORTANT:
- Focus ONLY on information directly relevant to "${query}"
- Preserve important data, statistics, quotes, and citations
- Ignore navigation, ads, and irrelevant content
- Keep total response under ${this.config.targetTokensPerSource} tokens

CONTENT TO PROCESS:
${content.slice(0, 50000)}`; // Limit input to prevent context overflow
  }

  private parseProcessedContent(text: string): {
    keyPoints: string;
    relevantSections: string;
    summary: string;
  } {
    const sections = text.split(/---[\w\s]+---/);
    
    // Extract sections with fallbacks
    const keyPointsMatch = text.match(/---KEY POINTS---([\s\S]*?)(?=---|\z)/);
    const relevantMatch = text.match(/---RELEVANT SECTIONS---([\s\S]*?)(?=---|\z)/);
    const summaryMatch = text.match(/---SUMMARY---([\s\S]*?)$/);
    
    return {
      keyPoints: keyPointsMatch?.[1]?.trim() || 'No key points extracted',
      relevantSections: relevantMatch?.[1]?.trim() || 'No relevant sections found',
      summary: summaryMatch?.[1]?.trim() || 'No summary available',
    };
  }

  private formatAsMarkdown(
    processed: { keyPoints: string; relevantSections: string; summary: string },
    result: SearchResult
  ): string {
    return `# ${result.title || 'Untitled'}
**Source:** ${result.url}

## Key Points
${processed.keyPoints}

## Relevant Content
${processed.relevantSections}

## Summary
${processed.summary}`;
  }

  private createFallbackResult(result: SearchResult, error: unknown): ProcessedResult {
    const fallbackContent = (result.content || '').slice(0, 2000) + '...';
    const tokens = this.estimateTokens(fallbackContent);
    
    return {
      url: result.url,
      title: result.title || 'Untitled',
      keyPoints: 'Failed to extract key points',
      relevantSections: fallbackContent,
      summary: `Processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      metadata: {
        originalTokens: this.estimateTokens(result.content || ''),
        processedTokens: tokens,
        compressionRatio: 1,
        model: MODELS[this.config.model].name,
        processingTimeMs: 0,
        retries: RETRY_CONFIG.maxRetries,
      },
      markdown: `# ${result.title || 'Untitled'}\n**Source:** ${result.url}\n\n## Error\nFailed to process content\n\n## Raw Extract\n${fallbackContent}`,
    };
  }

  private async callWithRetry<T>(
    fn: () => Promise<T>,
    attempt: number
  ): Promise<T> {
    try {
      return await fn();
    } catch (error) {
      if (attempt < RETRY_CONFIG.maxRetries - 1) {
        throw error; // Let the main retry logic handle it
      }
      throw error;
    }
  }

  private estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Public method to get current token usage report
  getTokenReport() {
    return { totalTokens: 0, totalCost: 0 }; // Simplified for compatibility
  }

  // Batch processing with concurrency control
  async preprocessBatch(
    results: SearchResult[],
    query: string,
    concurrency = 3
  ): Promise<ProcessedResult[]> {
    const batches: SearchResult[][] = [];
    
    // Split into batches
    for (let i = 0; i < results.length; i += concurrency) {
      batches.push(results.slice(i, i + concurrency));
    }
    
    const processedResults: ProcessedResult[] = [];
    
    // Process batches sequentially, items within batch in parallel
    for (const batch of batches) {
      const batchResults = await Promise.all(
        batch.map(result => this.preprocessSearchResult(result, query))
      );
      processedResults.push(...batchResults);
    }
    
    return processedResults;
  }
}