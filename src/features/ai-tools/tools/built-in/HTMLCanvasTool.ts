/**
 * HTML Canvas Tool Plugin
 * 
 * Creates interactive HTML/CSS/JavaScript canvases for web development.
 * Enables AI to generate and display web pages, components, and interactive demos.
 */

import { z } from 'zod'
import { BaseToolPlugin } from '../../base/BaseToolPlugin'
import { ToolCategory } from '../../types/core'
import type { ToolExecutionContext } from '../../types/core'

/**
 * Input schema for HTML Canvas tool
 */
const HTMLCanvasInputSchema = z.object({
  html: z.string().min(10).describe('Complete HTML content including CSS and JavaScript. Should include <!DOCTYPE html>, <html>, <head>, and <body> tags for a complete webpage.'),
  title: z.string().optional().describe('Optional title for the canvas (defaults to "HTML Canvas")')
}).required().describe('HTML Canvas creation parameters')

export type HTMLCanvasInput = z.infer<typeof HTMLCanvasInputSchema>

/**
 * Output interface for HTML Canvas results
 */
export interface HTMLCanvasResult {
  success: boolean
  artifactId?: string
  title: string
  message: string
  canvasUrl?: string
  error?: string
  metadata: {
    htmlLength: number
    hasCSS: boolean
    hasJavaScript: boolean
    hasInteractivity: boolean
    timestamp: string
  }
}

/**
 * HTML Canvas Tool Plugin
 * Creates interactive HTML/CSS/JavaScript workspaces
 */
export class HTMLCanvasTool extends BaseToolPlugin {
  readonly id = 'createHTMLCanvas'
  readonly name = 'HTML Canvas Creator'
  readonly description = 'Create interactive HTML/CSS/JavaScript canvases for web development, prototyping, and demonstrations'
  readonly category = ToolCategory.PRODUCTIVITY
  readonly version = '1.0.0'
  readonly inputSchema = HTMLCanvasInputSchema
  readonly permissions = [] // No special permissions needed

  protected async executeInternal(
    input: HTMLCanvasInput,
    context: ToolExecutionContext
  ): Promise<HTMLCanvasResult> {
    const { html, title = 'HTML Canvas' } = input
    
    console.log('[HTML_CANVAS] Creating canvas:', {
      title,
      htmlLength: html.length,
      hasUser: !!context.session?.userId,
      timestamp: new Date().toISOString()
    })

    try {
      // Analyze the HTML content
      const metadata = this.analyzeHTML(html)
      
      // Create the artifact via API
      const artifact = await this.createArtifact(html, title, context)
      
      const result: HTMLCanvasResult = {
        success: true,
        artifactId: artifact.id,
        title: artifact.title,
        message: `✨ Created HTML Canvas: "${title}"\n\n🎨 **Features detected:**\n${this.getFeatureDescription(metadata)}\n\n📝 **Instructions:**\n- The canvas will open automatically in the chat interface\n- Edit the code in the left panel\n- See live preview in the right panel\n- Click "Save" to update your changes\n- Use "Run" to refresh the preview`,
        canvasUrl: `/canvas/${artifact.id}`,
        metadata: {
          ...metadata,
          timestamp: new Date().toISOString()
        }
      }
      
      console.log('[HTML_CANVAS] Canvas created successfully:', {
        artifactId: result.artifactId,
        title: result.title,
        features: metadata
      })

      return result

    } catch (error) {
      console.error('[HTML_CANVAS] Error creating canvas:', error)
      
      return {
        success: false,
        title,
        message: `❌ Failed to create HTML Canvas: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          htmlLength: html.length,
          hasCSS: false,
          hasJavaScript: false,
          hasInteractivity: false,
          timestamp: new Date().toISOString()
        }
      }
    }
  }

  /**
   * Create artifact via API call
   */
  private async createArtifact(html: string, title: string, context: ToolExecutionContext) {
    // Make API call to create artifact
    const response = await fetch('/api/artifacts', {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        // Include session/auth headers if available
        ...(context.request?.headers && { 'Cookie': context.request.headers.cookie || '' })
      },
      body: JSON.stringify({
        content: html,
        type: 'html',
        title
      })
    })

    if (!response.ok) {
      throw new Error(`Failed to create artifact: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    
    if (!data.artifact) {
      throw new Error('Invalid response from artifacts API')
    }

    return data.artifact
  }

  /**
   * Analyze HTML content to detect features
   */
  private analyzeHTML(html: string) {
    const hasCSS = /<style[\s\S]*?<\/style>/i.test(html) || /style\s*=/i.test(html)
    const hasJavaScript = /<script[\s\S]*?<\/script>/i.test(html) || /onclick\s*=/i.test(html) || /addEventListener/i.test(html)
    const hasInteractivity = /onclick|onchange|onsubmit|addEventListener|querySelector/i.test(html)
    
    return {
      htmlLength: html.length,
      hasCSS,
      hasJavaScript,
      hasInteractivity
    }
  }

  /**
   * Get human-readable feature description
   */
  private getFeatureDescription(metadata: ReturnType<typeof HTMLCanvasTool.prototype.analyzeHTML>): string {
    const features = []
    
    if (metadata.hasCSS) features.push('🎨 Custom CSS styling')
    if (metadata.hasJavaScript) features.push('⚡ JavaScript functionality')
    if (metadata.hasInteractivity) features.push('🖱️ Interactive elements')
    
    if (features.length === 0) {
      features.push('📄 Basic HTML structure')
    }
    
    return features.join('\n')
  }

  /**
   * Validate HTML Canvas input
   */
  async validate(input: unknown): Promise<boolean> {
    try {
      const parsed = this.inputSchema.parse(input) as HTMLCanvasInput
      
      if (parsed.html.length < 10) {
        throw new Error('HTML content must be at least 10 characters long')
      }

      // Basic HTML validation
      if (!parsed.html.includes('<') || !parsed.html.includes('>')) {
        throw new Error('HTML content must contain valid HTML tags')
      }

      return true

    } catch (error) {
      throw error
    }
  }

  /**
   * Get enhanced tool summary
   */
  getSummary() {
    return {
      ...super.getSummary(),
      capabilities: [
        'Create interactive HTML/CSS/JavaScript canvases',
        'Live preview with Monaco Editor',
        'Automatic save functionality',
        'Feature detection and analysis',
        'iframe-based secure execution',
        'Mobile responsive editing'
      ],
      limitations: [
        'No external API access from canvas',
        'Limited to client-side functionality',
        'Requires user authentication',
        'Canvas content is sandboxed'
      ],
      examples: [
        { 
          html: '<!DOCTYPE html><html><head><title>Hello</title></head><body><h1>Hello World!</h1></body></html>', 
          description: 'Create a simple webpage' 
        },
        { 
          html: '<!DOCTYPE html><html><head><style>body{background:linear-gradient(45deg,#667eea,#764ba2)}</style></head><body><h1>Gradient Background</h1></body></html>', 
          description: 'Create a page with CSS styling' 
        },
        { 
          html: '<!DOCTYPE html><html><head></head><body><button onclick="alert(\"Hello!\")">Click me</button></body></html>', 
          description: 'Create an interactive button' 
        }
      ],
      supportedFeatures: [
        'HTML5 elements', 'CSS3 styling', 'JavaScript ES6+', 'Canvas API', 
        'SVG graphics', 'CSS animations', 'Event handling', 'DOM manipulation'
      ]
    }
  }
}