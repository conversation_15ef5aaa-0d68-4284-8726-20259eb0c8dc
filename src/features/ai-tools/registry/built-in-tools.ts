/**
 * Built-in Tools Registry
 * 
 * Registers all built-in tool plugins with the tool registry.
 * This file imports and exports all available built-in tools.
 */

import type { ToolPlugin } from '../types/core'

// Import all built-in tools
import { WebSearchTool } from '../tools/built-in/WebSearchTool'
import { WeatherSearchTool } from '../tools/built-in/WeatherSearchTool'
import { CalculatorTool } from '../tools/built-in/CalculatorTool'
import { TimeTool } from '../tools/built-in/TimeTool'
import { CodeAnalysisTool } from '../tools/built-in/CodeAnalysisTool'
import { PerplexityTool } from '../tools/built-in/PerplexityTool'
import { Context7Tool } from '../tools/built-in/Context7Tool'
import { FirecrawlTool } from '../tools/built-in/FirecrawlTool'
import { HTMLCanvasTool } from '../tools/built-in/HTMLCanvasTool'

/**
 * Create instances of all built-in tools
 */
const createBuiltInTools = (): ToolPlugin[] => {
  return [
    new WeatherSearchTool(), // Simple weather via web search - DEMO ENHANCED LOGGING
    new TimeTool(), // Simple time tool - Fixed schema for OpenAI compatibility
    new Context7Tool(), // Library documentation lookup via MCP
    new FirecrawlTool(), // Deep web research via MCP
    new PerplexityTool(), // AI search and reasoning via MCP
    new HTMLCanvasTool(), // Interactive HTML/CSS/JavaScript canvas creator
    // Temporarily disabled tools with schema issues:
    // new WebSearchTool(),
    // new CalculatorTool(),
    // new CodeAnalysisTool()
  ]
}

/**
 * Get all built-in tools
 * This function is called by the ToolRegistry during initialization
 */
export async function getBuiltInTools(): Promise<ToolPlugin[]> {
  try {
    console.log('[TOOLS DEBUG] getBuiltInTools() called')
    const tools = createBuiltInTools()
    console.log('[TOOLS DEBUG] Built-in tools created:', tools.length)
    
    // Validate all tools before returning
    for (const tool of tools) {
      console.log('[TOOLS DEBUG] Validating tool:', tool.id)
      validateBuiltInTool(tool)
      console.log('[TOOLS DEBUG] Tool validated successfully:', tool.id)
    }

    console.log('[TOOLS DEBUG] All tools validated, returning:', tools.map(t => t.id))
    return tools

  } catch (error) {
    console.error('[TOOLS DEBUG] Failed to create built-in tools:', error)
    // Return empty array rather than failing completely
    return []
  }
}

/**
 * Validate a built-in tool meets requirements
 */
function validateBuiltInTool(tool: ToolPlugin): void {
  if (!tool.id) {
    throw new Error(`Built-in tool missing ID: ${tool.constructor.name}`)
  }

  if (!tool.name) {
    throw new Error(`Built-in tool ${tool.id} missing name`)
  }

  if (!tool.description) {
    throw new Error(`Built-in tool ${tool.id} missing description`)
  }

  if (typeof tool.execute !== 'function') {
    throw new Error(`Built-in tool ${tool.id} missing execute function`)
  }

  if (!tool.inputSchema) {
    throw new Error(`Built-in tool ${tool.id} missing input schema`)
  }

  if (!tool.category) {
    throw new Error(`Built-in tool ${tool.id} missing category`)
  }
}

/**
 * Get built-in tools by category
 */
export function getBuiltInToolsByCategory(category: string): ToolPlugin[] {
  const allTools = createBuiltInTools()
  return allTools.filter(tool => tool.category === category)
}

/**
 * Get a specific built-in tool by ID
 */
export function getBuiltInTool(toolId: string): ToolPlugin | null {
  const allTools = createBuiltInTools()
  return allTools.find(tool => tool.id === toolId) || null
}

/**
 * Get metadata about all built-in tools
 */
export function getBuiltInToolsMetadata() {
  const tools = createBuiltInTools()
  
  return {
    totalTools: tools.length,
    categories: [...new Set(tools.map(tool => tool.category))],
    tools: tools.map(tool => ({
      id: tool.id,
      name: tool.name,
      description: tool.description,
      category: tool.category,
      version: tool.version,
      permissions: tool.permissions,
      clientSide: tool.clientSide || false,
      requiresConfirmation: tool.requiresConfirmation || false
    }))
  }
}

/**
 * Export individual tools for direct import if needed
 */
export {
  WebSearchTool,
  WeatherTool,
  CalculatorTool,
  TimeTool,
  CodeAnalysisTool
}

/**
 * Tool configuration and feature flags
 */
export const BUILT_IN_TOOLS_CONFIG = {
  // Feature flags for enabling/disabling specific tools
  enableWebSearch: process.env.TOOL_WEB_SEARCH_ENABLED !== 'false',
  enableWeather: process.env.TOOL_WEATHER_ENABLED !== 'false',
  enableCalculator: process.env.TOOL_CALCULATOR_ENABLED !== 'false',
  enableTime: process.env.TOOL_TIME_ENABLED !== 'false',
  enableCodeAnalysis: process.env.TOOL_CODE_ANALYSIS_ENABLED !== 'false',

  // Tool-specific configuration
  webSearch: {
    defaultProvider: process.env.WEB_SEARCH_PROVIDER || 'auto', // 'brave' | 'firecrawl' | 'auto'
    maxResults: parseInt(process.env.WEB_SEARCH_MAX_RESULTS || '5'),
    rateLimitPerMinute: parseInt(process.env.WEB_SEARCH_RATE_LIMIT || '10')
  },

  weather: {
    provider: process.env.WEATHER_PROVIDER || 'mock', // 'openweather' | 'accuweather' | 'mock'
    apiKey: process.env.WEATHER_API_KEY,
    defaultUnits: process.env.WEATHER_DEFAULT_UNITS || 'celsius'
  },

  calculator: {
    maxExpressionLength: parseInt(process.env.CALCULATOR_MAX_EXPRESSION_LENGTH || '1000'),
    enableScientificFunctions: process.env.CALCULATOR_SCIENTIFIC_ENABLED !== 'false'
  },

  codeAnalysis: {
    maxCodeLength: parseInt(process.env.CODE_ANALYSIS_MAX_LENGTH || '50000'),
    enableSecurityAnalysis: process.env.CODE_ANALYSIS_SECURITY_ENABLED !== 'false',
    strictMode: process.env.CODE_ANALYSIS_STRICT_MODE === 'true'
  }
}

/**
 * Get filtered built-in tools based on configuration and permissions
 */
export async function getFilteredBuiltInTools(context?: {
  enabledTools?: string[]
  userPermissions?: string[]
  environmentRestrictions?: string[]
}): Promise<ToolPlugin[]> {
  const allTools = await getBuiltInTools()
  
  if (!context) {
    return allTools
  }

  return allTools.filter(tool => {
    // Check if tool is explicitly enabled
    if (context.enabledTools && !context.enabledTools.includes(tool.id)) {
      return false
    }

    // Check feature flags
    switch (tool.id) {
      case 'webSearch':
        return BUILT_IN_TOOLS_CONFIG.enableWebSearch
      case 'getWeather':
        return BUILT_IN_TOOLS_CONFIG.enableWeather
      case 'calculate':
        return BUILT_IN_TOOLS_CONFIG.enableCalculator
      case 'getCurrentTime':
        return BUILT_IN_TOOLS_CONFIG.enableTime
      case 'analyzeCode':
        return BUILT_IN_TOOLS_CONFIG.enableCodeAnalysis
      default:
        return true
    }
  })
}

/**
 * Tool usage statistics and monitoring
 */
export class BuiltInToolsMonitor {
  private static instance: BuiltInToolsMonitor | null = null
  private usageStats: Map<string, {
    callCount: number
    totalExecutionTime: number
    errorCount: number
    lastUsed: Date
  }> = new Map()

  static getInstance(): BuiltInToolsMonitor {
    if (!BuiltInToolsMonitor.instance) {
      BuiltInToolsMonitor.instance = new BuiltInToolsMonitor()
    }
    return BuiltInToolsMonitor.instance
  }

  recordUsage(toolId: string, executionTime: number, success: boolean): void {
    if (!this.usageStats.has(toolId)) {
      this.usageStats.set(toolId, {
        callCount: 0,
        totalExecutionTime: 0,
        errorCount: 0,
        lastUsed: new Date()
      })
    }

    const stats = this.usageStats.get(toolId)!
    stats.callCount++
    stats.totalExecutionTime += executionTime
    stats.lastUsed = new Date()
    
    if (!success) {
      stats.errorCount++
    }
  }

  getStats(toolId?: string) {
    if (toolId) {
      return this.usageStats.get(toolId) || null
    }
    
    return Object.fromEntries(this.usageStats.entries())
  }

  getAggregateStats() {
    const stats = Array.from(this.usageStats.values())
    
    return {
      totalCalls: stats.reduce((sum, s) => sum + s.callCount, 0),
      totalExecutionTime: stats.reduce((sum, s) => sum + s.totalExecutionTime, 0),
      totalErrors: stats.reduce((sum, s) => sum + s.errorCount, 0),
      averageExecutionTime: stats.length > 0 
        ? stats.reduce((sum, s) => sum + s.totalExecutionTime, 0) / stats.reduce((sum, s) => sum + s.callCount, 0)
        : 0,
      successRate: stats.length > 0
        ? 1 - (stats.reduce((sum, s) => sum + s.errorCount, 0) / stats.reduce((sum, s) => sum + s.callCount, 0))
        : 0
    }
  }
}

/**
 * Initialize built-in tools monitoring
 */
export function initializeBuiltInToolsMonitoring(): BuiltInToolsMonitor {
  return BuiltInToolsMonitor.getInstance()
}