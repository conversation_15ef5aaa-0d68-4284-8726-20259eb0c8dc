/**
 * Model Selection Utility
 */

import { deepResearchConfig } from '../config';
import { getAISDKModel } from '@/lib/ai/providers';

export class ModelSelector {
  private config = deepResearchConfig.getConfig();
  
  async selectModel(agentType: string, userPlan?: string): Promise<any> {
    const modelConfig = this.config.models[agentType as keyof typeof this.config.models];
    
    // Check if it's a string (model ID) or object (selection/fallbacks)
    const modelId = typeof modelConfig === 'string' ? modelConfig : 'openai/gpt-4o-mini';
    return getAISDKModel(modelId);
  }
  
  async getFallbackModels(agentType: string): Promise<any[]> {
    const fallbacks = this.config.models.fallbacks[agentType as keyof typeof this.config.models.fallbacks];
    return fallbacks ? fallbacks.map(id => getAISDKModel(id)) : [];
  }
}