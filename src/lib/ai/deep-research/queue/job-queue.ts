/**
 * Redis-based Job Queue for Deep Research Tasks
 * Proper architecture: API triggers job, workers process in background
 */
import { Redis } from 'ioredis';
import { routerLogger } from '@/lib/logger';
import { DeepResearchRequest, ResearchStatus, ResearchSessionStatus } from '../types';

export interface DeepResearchJob {
  id: string;
  userId: string;
  request: DeepResearchRequest;
  status: ResearchStatus;
  priority: 'low' | 'normal' | 'high';
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  progress: number;
  currentPhase: string;
  error?: string;
  result?: any;
  metadata?: Record<string, any>;
}

export interface QueueStats {
  pending: number;
  processing: number;
  completed: number;
  failed: number;
  workers: number;
}

export class DeepResearchJobQueue {
  private redis: Redis;
  private readonly queueName = 'deep-research-jobs';
  private readonly processingSetName = 'deep-research-processing';
  private readonly statusKeyPrefix = 'deep-research-status:';
  private readonly resultKeyPrefix = 'deep-research-result:';

  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      maxRetriesPerRequest: 3,
    });

    this.redis.on('error', (error) => {
      routerLogger.error('Redis connection error in DeepResearchJobQueue', { error });
    });
  }

  /**
   * Add a new research job to the queue
   */
  async enqueue(job: Omit<DeepResearchJob, 'status' | 'progress' | 'currentPhase' | 'createdAt'>): Promise<string> {
    const fullJob: DeepResearchJob = {
      ...job,
      status: ResearchSessionStatus.QUEUED,
      progress: 0,
      currentPhase: 'queued',
      createdAt: new Date(),
    };

    // Store job status
    await this.redis.setex(
      `${this.statusKeyPrefix}${job.id}`,
      3600 * 24, // 24 hours TTL
      JSON.stringify(fullJob)
    );

    // Add to queue with priority
    const score = this.getPriorityScore(job.priority);
    await this.redis.zadd(this.queueName, score, job.id);

    routerLogger.info('Job enqueued for deep research', {
      jobId: job.id,
      userId: job.userId,
      priority: job.priority,
      query: job.request.query.substring(0, 100)
    });

    return job.id;
  }

  /**
   * Get the next job from the queue for processing
   */
  async dequeue(): Promise<DeepResearchJob | null> {
    // Get highest priority job
    const results = await this.redis.zpopmin(this.queueName);
    
    if (!results || results.length < 2) {
      return null;
    }

    const jobId = results[0] as string;
    
    // Move to processing set
    await this.redis.sadd(this.processingSetName, jobId);
    
    // Get job details
    const jobData = await this.getJobStatus(jobId);
    if (!jobData) {
      // Clean up orphaned job
      await this.redis.srem(this.processingSetName, jobId);
      return null;
    }

    // Update status to processing
    jobData.status = ResearchSessionStatus.RUNNING;
    jobData.startedAt = new Date();
    await this.updateJobStatus(jobId, jobData);

    return jobData;
  }

  /**
   * Update job status and progress
   */
  async updateJobStatus(jobId: string, updates: Partial<DeepResearchJob>): Promise<void> {
    const currentJob = await this.getJobStatus(jobId);
    if (!currentJob) {
      routerLogger.warn('Attempted to update non-existent job', { jobId });
      return;
    }

    const updatedJob = { ...currentJob, ...updates };
    
    await this.redis.setex(
      `${this.statusKeyPrefix}${jobId}`,
      3600 * 24, // 24 hours TTL
      JSON.stringify(updatedJob)
    );

    // If job is completed or failed, remove from processing
    if (updates.status === 'completed' || updates.status === 'failed') {
      await this.redis.srem(this.processingSetName, jobId);
      
      if (updates.status === 'completed') {
        updatedJob.completedAt = new Date();
      }
    }
  }

  /**
   * Get job status by ID
   */
  async getJobStatus(jobId: string): Promise<DeepResearchJob | null> {
    const data = await this.redis.get(`${this.statusKeyPrefix}${jobId}`);
    if (!data) return null;

    try {
      const job = JSON.parse(data);
      // Convert date strings back to Date objects
      job.createdAt = new Date(job.createdAt);
      if (job.startedAt) job.startedAt = new Date(job.startedAt);
      if (job.completedAt) job.completedAt = new Date(job.completedAt);
      return job;
    } catch (error) {
      routerLogger.error('Failed to parse job data', { jobId, error });
      return null;
    }
  }

  /**
   * Store job result
   */
  async storeResult(jobId: string, result: any): Promise<void> {
    await this.redis.setex(
      `${this.resultKeyPrefix}${jobId}`,
      3600 * 24 * 7, // 7 days TTL for results
      JSON.stringify(result)
    );
  }

  /**
   * Get job result
   */
  async getResult(jobId: string): Promise<any | null> {
    const data = await this.redis.get(`${this.resultKeyPrefix}${jobId}`);
    return data ? JSON.parse(data) : null;
  }

  /**
   * Cancel a job
   */
  async cancelJob(jobId: string): Promise<boolean> {
    const job = await this.getJobStatus(jobId);
    if (!job) return false;

    if (job.status === ResearchSessionStatus.QUEUED) {
      // Remove from queue
      await this.redis.zrem(this.queueName, jobId);
    } else if (job.status === ResearchSessionStatus.RUNNING) {
      // Mark as cancelled - worker should check this
      await this.updateJobStatus(jobId, { 
        status: ResearchSessionStatus.CANCELLED,
        currentPhase: 'cancelled'
      });
      return true;
    }

    // Update status
    await this.updateJobStatus(jobId, { 
      status: ResearchSessionStatus.CANCELLED,
      currentPhase: 'cancelled'
    });

    await this.redis.srem(this.processingSetName, jobId);
    return true;
  }

  /**
   * Get queue statistics
   */
  async getStats(): Promise<QueueStats> {
    const [pending, processing] = await Promise.all([
      this.redis.zcard(this.queueName),
      this.redis.scard(this.processingSetName)
    ]);

    // Count completed and failed jobs (rough estimate)
    const allStatusKeys = await this.redis.keys(`${this.statusKeyPrefix}*`);
    let completed = 0;
    let failed = 0;

    // Sample recent jobs for stats (limit to avoid performance issues)
    const sampleKeys = allStatusKeys.slice(0, 100);
    const statusPromises = sampleKeys.map(key => this.redis.get(key));
    const statuses = await Promise.all(statusPromises);

    for (const status of statuses) {
      if (status) {
        try {
          const job = JSON.parse(status);
          if (job.status === 'completed') completed++;
          else if (job.status === 'failed') failed++;
        } catch (e) {
          // Ignore parse errors
        }
      }
    }

    return {
      pending,
      processing,
      completed,
      failed,
      workers: 1 // TODO: Track actual worker count
    };
  }

  /**
   * Get queue statistics (alias for getStats for compatibility)
   */
  async getQueueStats(): Promise<QueueStats> {
    return this.getStats();
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.redis.ping();
      return true;
    } catch (error) {
      routerLogger.error('Redis health check failed', { error });
      return false;
    }
  }

  /**
   * Clean up old jobs
   */
  async cleanup(olderThanHours: number = 24): Promise<number> {
    const cutoff = Date.now() - (olderThanHours * 60 * 60 * 1000);
    let cleaned = 0;

    // Clean completed/failed jobs older than cutoff
    const statusKeys = await this.redis.keys(`${this.statusKeyPrefix}*`);
    
    for (const key of statusKeys) {
      const data = await this.redis.get(key);
      if (data) {
        try {
          const job = JSON.parse(data);
          const jobTime = new Date(job.completedAt || job.createdAt).getTime();
          
          if (jobTime < cutoff && ['completed', 'failed', 'cancelled'].includes(job.status)) {
            await this.redis.del(key);
            await this.redis.del(`${this.resultKeyPrefix}${job.id}`);
            cleaned++;
          }
        } catch (e) {
          // Delete corrupted entries
          await this.redis.del(key);
          cleaned++;
        }
      }
    }

    routerLogger.info('Job queue cleanup completed', { 
      cleaned, 
      olderThanHours 
    });

    return cleaned;
  }

  /**
   * Get priority score for Redis sorted set
   */
  private getPriorityScore(priority: string): number {
    switch (priority) {
      case 'high': return Date.now() - 1000000; // Higher priority = lower score
      case 'normal': return Date.now();
      case 'low': return Date.now() + 1000000;
      default: return Date.now();
    }
  }

  /**
   * Close Redis connection
   */
  async close(): Promise<void> {
    await this.redis.quit();
  }
}

// Singleton instance
export const deepResearchQueue = new DeepResearchJobQueue();