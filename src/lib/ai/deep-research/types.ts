/**
 * Deep Research Module - Type Definitions
 * 
 * @description
 * Core type definitions for the Deep Research multi-agent system.
 * Supports AI SDK v5 integration, streaming progress updates, and
 * sophisticated research coordination with real-time feedback.
 * 
 * @module lib/ai/deep-research/types
 */

import type { 
  SearchResult, 
  User, 
  Model,
  RouterDecision
} from '@/types';
import type { 
  AIMessage, 
  ModelInfo 
} from '@/lib/ai/providers/types';

// ===== CORE RESEARCH TYPES =====

/**
 * Deep research request configuration
 */
export interface DeepResearchRequest {
  query: string;
  userId?: string;
  sessionId?: string;
  options?: DeepResearchOptions;
  context?: ResearchContext;
}

/**
 * Comprehensive research configuration options
 */
export interface DeepResearchOptions {
  // Research scope and depth
  maxDepth?: number;            // Maximum research iterations (default: 3, max: 10)
  timeLimit?: number;           // Maximum research time in seconds (default: 120, max: 600)
  maxUrls?: number;            // Maximum URLs to analyze (default: 50, max: 200)
  maxConcurrentAgents?: number; // Maximum concurrent agents (default: 5, max: 10)
  
  // Quality and thoroughness
  researchQuality?: 'basic' | 'comprehensive' | 'exhaustive';
  analysisDepth?: 'surface' | 'moderate' | 'deep';
  factCheckingLevel?: 'none' | 'basic' | 'rigorous';
  
  // Source filtering and preferences
  domainFilters?: {
    include?: string[];         // Only search these domains
    exclude?: string[];         // Exclude these domains
    trustLevel?: 'all' | 'verified' | 'academic'; // Trust level for sources
  };
  
  // Search configuration
  searchOptions?: {
    recency?: 'auto' | 'hour' | 'day' | 'week' | 'month';
    language?: string;
    region?: string;
    includeImages?: boolean;
    includeNews?: boolean;
  };
  
  // Output preferences
  outputFormat?: 'markdown' | 'json' | 'structured';
  includeReferences?: boolean;
  includeSummary?: boolean;
  maxOutputLength?: number;
  
  // Advanced options
  enableParallelization?: boolean;
  enableCaching?: boolean;
  customInstructions?: string;
  tools?: ResearchTool[];
}

/**
 * Research context for personalization and optimization
 */
export interface ResearchContext {
  user?: User;
  previousQueries?: string[];
  relatedConversations?: string[];
  domainExpertise?: string[];
  preferredSources?: string[];
  language?: string;
  timezone?: string;
}

// ===== AGENT SYSTEM TYPES =====

/**
 * Research agent types and their specializations
 */
export enum AgentType {
  RESEARCH = 'research',           // Primary research and information gathering
  ANALYSIS = 'analysis',           // Deep analysis and synthesis
  FACT_CHECK = 'fact_check',       // Fact verification and source validation
  COMPRESSION = 'compression',     // Information compression and summarization
  REPORT = 'report',              // Report generation and formatting
  COORDINATOR = 'coordinator'      // Agent coordination and workflow management
}

/**
 * Agent status and state management
 */
export enum AgentStatus {
  IDLE = 'idle',
  INITIALIZING = 'initializing',
  ACTIVE = 'active',
  WAITING = 'waiting',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

/**
 * Individual research agent definition interface
 */
export interface IResearchAgent {
  id: string;
  type: AgentType;
  name: string;
  description: string;
  status: AgentStatus;
  model: Model;
  
  // Capabilities and configuration
  capabilities: AgentCapability[];
  specialization?: string;
  maxConcurrentTasks?: number;
  timeout?: number;
  
  // Performance tracking
  metrics: AgentMetrics;
  
  // Current state
  currentTask?: AgentTask;
  lastActivity?: Date;
  
  // Configuration
  systemPrompt?: string;
  tools?: ResearchTool[];
  parameters?: AgentParameters;
}

/**
 * Agent capabilities enumeration
 */
export enum AgentCapability {
  WEB_SEARCH = 'web_search',
  DOCUMENT_ANALYSIS = 'document_analysis',
  FACT_CHECKING = 'fact_checking',
  DATA_SYNTHESIS = 'data_synthesis',
  CONTENT_GENERATION = 'content_generation',
  SOURCE_VALIDATION = 'source_validation',
  CITATION_MANAGEMENT = 'citation_management',
  REASONING = 'reasoning',
  MULTILINGUAL = 'multilingual'
}

/**
 * Agent task definition and execution
 */
export interface AgentTask {
  id: string;
  agentId: string;
  type: AgentTaskType;
  priority: TaskPriority;
  status: AgentStatus;
  
  // Task content
  query: string;
  context?: any;
  parameters?: Record<string, any>;
  
  // Execution tracking
  startTime?: Date;
  endTime?: Date;
  duration?: number;
  
  // Results
  result?: AgentTaskResult;
  error?: ResearchError;
  
  // Dependencies
  dependencies?: string[];
  dependents?: string[];
}

/**
 * Types of tasks agents can perform
 */
export enum AgentTaskType {
  SEARCH = 'search',
  ANALYZE = 'analyze',
  FACT_CHECK = 'fact_check',
  SYNTHESIZE = 'synthesize',
  GENERATE_REPORT = 'generate_report',
  VALIDATE_SOURCES = 'validate_sources',
  EXTRACT_DATA = 'extract_data'
}

/**
 * Task priority levels
 */
export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * Agent task execution result
 */
export interface AgentTaskResult {
  success: boolean;
  data: any;
  sources?: SearchResult[];
  confidence?: number;
  metadata?: Record<string, any>;
  citations?: string[];
  processingTime?: number;
  tokensUsed?: number;
  tokenUsage?: TokenUsage; // NEW: Detailed token tracking
}

/**
 * Token usage tracking for cost calculation
 */
export interface TokenUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  model: string;
  provider?: string;
  estimatedCost?: number;
  timestamp?: Date;
}

/**
 * Token tracking and cost calculation utility
 */
export class TokenTracker {
  private static readonly PRICING: Record<string, { prompt: number; completion: number }> = {
    // Anthropic Claude pricing per 1K tokens (from DB, converted from per 1M)
    'claude-3-5-sonnet': { prompt: 0.003, completion: 0.015 },
    'claude-3-5-sonnet-20241022': { prompt: 0.003, completion: 0.015 },
    'claude-3.5-haiku': { prompt: 0.0008, completion: 0.004 },
    'claude-3-opus-latest': { prompt: 0.015, completion: 0.075 },
    'claude-opus-4-0': { prompt: 0.015, completion: 0.075 },
    'claude-sonnet-4-0': { prompt: 0.003, completion: 0.015 },
    'claude-3-7-sonnet-thinking': { prompt: 0.009, completion: 0.045 }, // Thinking model
    'claude-opus-4-0-thinking': { prompt: 0.045, completion: 0.225 }, // Thinking model
    'claude-sonnet-4-0-thinking': { prompt: 0.012, completion: 0.06 }, // Thinking model
    
    // OpenAI pricing per 1K tokens
    'gpt-4o': { prompt: 0.005, completion: 0.015 },
    'gpt-4o-mini': { prompt: 0.00015, completion: 0.0006 },
    'gpt-4.1': { prompt: 0.002, completion: 0.008 },
    'gpt-4.1-mini': { prompt: 0.0004, completion: 0.0016 },
    'gpt-4.1-nano': { prompt: 0.0001, completion: 0.0004 },
    'o1': { prompt: 0.015, completion: 0.06 },
    'o1-mini': { prompt: 0.003, completion: 0.012 },
    'o1-pro': { prompt: 0.015, completion: 0.06 },
    'o3': { prompt: 0.002, completion: 0.008 },
    'o3-mini': { prompt: 0.0011, completion: 0.0044 },
    'o3-pro': { prompt: 0.02, completion: 0.08 },
    'o3-deep-research': { prompt: 0.01, completion: 0.04 },
    'o4-mini': { prompt: 0.0011, completion: 0.0044 },
    
    // Google pricing per 1K tokens
    'gemini-2.5-pro': { prompt: 0.00125, completion: 0.005 },
    'gemini-2.5-flash': { prompt: 0.000075, completion: 0.0003 },
    'gemini-2.5-flash-lite': { prompt: 0.0001, completion: 0.0004 },
    'gemini-2.0-flash-thinking-exp': { prompt: 0.000075, completion: 0.0003 },
    
    // Groq pricing per 1K tokens
    'deepseek-r1-distill-llama-70b': { prompt: 0.00075, completion: 0.00099 },
    'qwen/qwen3-32b': { prompt: 0.0001, completion: 0.0002 },
    
    // Perplexity pricing per 1K tokens
    'sonar-reasoning': { prompt: 0.001, completion: 0.005 },
    
    // xAI pricing per 1K tokens
    'grok-3-reasoning': { prompt: 0.005, completion: 0.015 },
    'grok-3-mini-reasoning': { prompt: 0.002, completion: 0.006 },
    'grok-4-0709': { prompt: 0.003, completion: 0.015 },
    
    // Fireworks AI (DeepSeek R1)
    'deepseek-r1': { prompt: 0.00055, completion: 0.00219 },
    'deepseek-r1-0528': { prompt: 0.00055, completion: 0.00219 },
    'deepseek-r1-basic': { prompt: 0.00055, completion: 0.00219 },
    'perplexity/r1-1776': { prompt: 0.001, completion: 0.001 }
  };

  /**
   * Track token usage from AI SDK v5 response
   */
  static track(
    model: string, 
    promptTokens: number, 
    completionTokens: number,
    provider?: string
  ): TokenUsage {
    const totalTokens = promptTokens + completionTokens;
    const estimatedCost = this.calculateCost(model, promptTokens, completionTokens);
    
    return {
      promptTokens,
      completionTokens,
      totalTokens,
      model,
      provider,
      estimatedCost,
      timestamp: new Date()
    };
  }

  /**
   * Calculate estimated cost based on model pricing
   */
  static calculateCost(
    model: string, 
    promptTokens: number, 
    completionTokens: number
  ): number {
    const pricing = this.PRICING[model];
    if (!pricing) {
      console.warn(`No pricing found for model: ${model}`);
      return 0;
    }

    const promptCost = (promptTokens / 1000) * pricing.prompt;
    const completionCost = (completionTokens / 1000) * pricing.completion;
    
    return Number((promptCost + completionCost).toFixed(6));
  }

  /**
   * Aggregate multiple token usage records
   */
  static aggregate(usages: TokenUsage[]): TokenUsage {
    const totals = usages.reduce(
      (acc, usage) => ({
        promptTokens: acc.promptTokens + usage.promptTokens,
        completionTokens: acc.completionTokens + usage.completionTokens,
        totalTokens: acc.totalTokens + usage.totalTokens,
        estimatedCost: acc.estimatedCost + (usage.estimatedCost || 0)
      }),
      { promptTokens: 0, completionTokens: 0, totalTokens: 0, estimatedCost: 0 }
    );

    // Get most used model
    const modelCounts = usages.reduce((acc, usage) => {
      acc[usage.model] = (acc[usage.model] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const mostUsedModel = Object.entries(modelCounts)
      .sort((a, b) => b[1] - a[1])[0]?.[0] || 'mixed';

    return {
      ...totals,
      model: mostUsedModel,
      provider: usages.length === 1 ? usages[0].provider : 'mixed',
      timestamp: new Date()
    };
  }

  /**
   * Format token usage as human-readable string
   */
  static format(usage: TokenUsage): string {
    const cost = usage.estimatedCost 
      ? `($${usage.estimatedCost.toFixed(4)})` 
      : '';
    
    return `${usage.totalTokens.toLocaleString()} tokens ${cost} - ${usage.model}`;
  }

  /**
   * Get pricing information for a model
   */
  static getPricing(model: string): { prompt: number; completion: number } | null {
    return this.PRICING[model] || null;
  }
}

/**
 * Agent performance metrics
 */
export interface AgentMetrics {
  tasksCompleted: number;
  tasksSuccessful: number;
  tasksFailed: number;
  averageProcessingTime: number;
  averageTokenUsage: number;
  reliabilityScore: number;
  lastPerformanceUpdate: Date;
}

/**
 * Agent configuration parameters
 */
export interface AgentParameters {
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  timeout?: number;
  retryAttempts?: number;
  parallelRequests?: number;
  customInstructions?: string;
}

// ===== STREAMING AND PROGRESS TYPES =====

/**
 * Research progress update for real-time streaming
 */
export interface ResearchProgressUpdate {
  type: ResearchProgressType;
  timestamp: Date;
  phase: ResearchPhase;
  
  // Progress indicators
  progress: number;              // 0-100 percentage
  currentStep: string;
  totalSteps?: number;
  estimatedTimeRemaining?: number;
  
  // Agent activity
  activeAgents?: number;
  completedAgents?: number;
  
  // Content updates
  message?: string;
  data?: any;
  sources?: SearchResult[];
  
  // Error handling
  error?: ResearchError;
  warning?: string;
  
  // Metadata
  agentId?: string;
  taskId?: string;
  sessionId?: string;
}

/**
 * Types of progress updates
 */
export enum ResearchProgressType {
  INITIALIZATION = 'initialization',
  SEARCH_START = 'search_start',
  SEARCH_PROGRESS = 'search_progress',
  SEARCH_COMPLETE = 'search_complete',
  AGENT_START = 'agent_start',
  AGENT_PROGRESS = 'agent_progress',
  AGENT_COMPLETE = 'agent_complete',
  ANALYSIS_START = 'analysis_start',
  ANALYSIS_PROGRESS = 'analysis_progress',
  ANALYSIS_COMPLETE = 'analysis_complete',
  SYNTHESIS_START = 'synthesis_start',
  SYNTHESIS_PROGRESS = 'synthesis_progress',
  SYNTHESIS_COMPLETE = 'synthesis_complete',
  REPORT_GENERATION = 'report_generation',
  COMPLETION = 'completion',
  ERROR = 'error',
  WARNING = 'warning'
}

/**
 * Research execution phases
 */
export enum ResearchPhase {
  PLANNING = 'planning',
  SEARCH = 'search',
  ANALYSIS = 'analysis',
  FACT_CHECK = 'fact_check',
  SYNTHESIS = 'synthesis',
  REPORT = 'report',
  VALIDATION = 'validation',
  COMPLETION = 'completion'
}

/**
 * Streaming response chunk for AI SDK v5 compatibility
 */
export interface ResearchStreamChunk {
  researchType?: ResearchProgressType;
  researchPhase?: ResearchPhase;
  researchData?: {
    progress?: number;
    activeAgents?: number;
    sources?: SearchResult[];
    analysis?: string;
    report?: string;
  };
}

// ===== RESULT AND OUTPUT TYPES =====

/**
 * Complete deep research result
 */
export interface DeepResearchResult {
  id: string;
  query: string;
  success: boolean;
  
  // Core results
  analysis: string;
  summary: string;
  report?: string;
  
  // Research data
  sources: EnhancedSearchResult[];
  findings: ResearchFinding[];
  citations: Citation[];
  
  // Quality metrics
  confidence: number;
  reliability: number;
  completeness: number;
  
  // Execution metadata
  executionTime: number;
  tokensUsed: number;
  tokenUsage?: TokenUsage; // NEW: Detailed token usage breakdown
  agentsUsed: AgentUsageInfo[];
  
  // Context and tracking
  sessionId: string;
  userId?: string;
  timestamp: Date;
  
  // Error handling
  errors?: ResearchError[];
  warnings?: string[];
  
  // Additional data
  metadata?: Record<string, any>;
  cacheKey?: string;
}

/**
 * Enhanced search result with deep research metadata
 */
export interface EnhancedSearchResult extends SearchResult {
  id: string;
  relevanceScore: number;
  credibilityScore: number;
  extractedContent?: string;
  keyFindings?: string[];
  factCheckStatus?: FactCheckStatus;
  lastVerified?: Date;
  processingAgent?: string;
  extractionMetadata?: {
    contentLength: number;
    readabilityScore?: number;
    sourceType: SourceType;
    authority: number;
  };
}

/**
 * Research finding with evidence and confidence
 */
export interface ResearchFinding {
  id: string;
  statement: string;
  evidence: Evidence[];
  confidence: number;
  category: FindingCategory;
  importance: number;
  contradictions?: string[];
  relatedFindings?: string[];
}

/**
 * Evidence supporting a research finding
 */
export interface Evidence {
  source: EnhancedSearchResult;
  relevantText: string;
  strength: EvidenceStrength;
  context?: string;
  extractionMethod: string;
}

/**
 * Citation with formatting and metadata
 */
export interface Citation {
  id: string;
  source: EnhancedSearchResult;
  citationText: string;
  format: CitationFormat;
  inTextReference: string;
  usageCount: number;
}

/**
 * Agent usage information for tracking
 */
export interface AgentUsageInfo {
  agentId: string;
  agentType: AgentType;
  tasksCompleted: number;
  executionTime: number;
  tokensUsed: number;
  tokenUsage?: TokenUsage; // NEW: Detailed token breakdown per agent
  successRate: number;
}

// ===== ENUMERATION TYPES =====

/**
 * Fact checking status enumeration
 */
export enum FactCheckStatus {
  VERIFIED = 'verified',
  PARTIALLY_VERIFIED = 'partially_verified',
  DISPUTED = 'disputed',
  UNVERIFIED = 'unverified',
  FALSE = 'false',
  PENDING = 'pending'
}

/**
 * Source type classification
 */
export enum SourceType {
  ACADEMIC = 'academic',
  NEWS = 'news',
  GOVERNMENT = 'government',
  CORPORATE = 'corporate',
  NONPROFIT = 'nonprofit',
  PERSONAL = 'personal',
  SOCIAL_MEDIA = 'social_media',
  WIKI = 'wiki',
  FORUM = 'forum',
  BLOG = 'blog',
  OTHER = 'other'
}

/**
 * Research finding categories
 */
export enum FindingCategory {
  FACT = 'fact',
  OPINION = 'opinion',
  STATISTIC = 'statistic',
  TREND = 'trend',
  HYPOTHESIS = 'hypothesis',
  CONCLUSION = 'conclusion',
  RECOMMENDATION = 'recommendation'
}

/**
 * Evidence strength levels
 */
export enum EvidenceStrength {
  WEAK = 'weak',
  MODERATE = 'moderate',
  STRONG = 'strong',
  VERY_STRONG = 'very_strong'
}

/**
 * Citation format options
 */
export enum CitationFormat {
  APA = 'apa',
  MLA = 'mla',
  CHICAGO = 'chicago',
  IEEE = 'ieee',
  HARVARD = 'harvard',
  SIMPLE = 'simple'
}

// ===== TOOL AND INTEGRATION TYPES =====

/**
 * Research tool definition
 */
export interface ResearchTool {
  id: string;
  name: string;
  description: string;
  type: ResearchToolType;
  
  // Configuration
  enabled: boolean;
  parameters?: Record<string, any>;
  apiKeys?: Record<string, string>;
  
  // Capabilities
  capabilities: ToolCapability[];
  rateLimits?: ToolRateLimit;
  
  // Integration
  handler: string;              // Function or class name to handle this tool
  priority: number;             // Execution priority
}

/**
 * Types of research tools
 */
export enum ResearchToolType {
  WEB_SEARCH = 'web_search',
  DOCUMENT_SCRAPER = 'document_scraper',
  FACT_CHECKER = 'fact_checker',
  DATA_EXTRACTOR = 'data_extractor',
  CITATION_GENERATOR = 'citation_generator',
  CONTENT_ANALYZER = 'content_analyzer',
  SOURCE_VALIDATOR = 'source_validator'
}

/**
 * Tool capabilities
 */
export enum ToolCapability {
  SEARCH = 'search',
  SCRAPE = 'scrape',
  EXTRACT = 'extract',
  VALIDATE = 'validate',
  ANALYZE = 'analyze',
  GENERATE = 'generate',
  TRANSLATE = 'translate'
}

/**
 * Tool rate limiting configuration
 */
export interface ToolRateLimit {
  requestsPerMinute: number;
  requestsPerHour: number;
  requestsPerDay: number;
  concurrentRequests: number;
  burstLimit?: number;
}

// ===== ERROR AND EXCEPTION TYPES =====

/**
 * Research-specific error class
 */
export class ResearchError extends Error {
  constructor(
    message: string,
    public researchPhase?: ResearchPhase,
    public agentId?: string,
    public taskId?: string,
    status?: number,
    details?: any
  ) {
    super(message);
    this.name = 'ResearchError';
  }
}

/**
 * Agent execution error
 */
export class AgentError extends ResearchError {
  constructor(
    message: string,
    public agentType: AgentType,
    agentId?: string,
    taskId?: string
  ) {
    super(message, undefined, agentId, taskId);
    this.name = 'AgentError';
  }
}

/**
 * Research timeout error
 */
export class ResearchTimeoutError extends ResearchError {
  constructor(
    message: string,
    public timeoutDuration: number,
    researchPhase?: ResearchPhase
  ) {
    super(message, researchPhase);
    this.name = 'ResearchTimeoutError';
  }
}

// ===== CONFIGURATION AND SYSTEM TYPES =====

/**
 * Deep research system configuration
 */
export interface DeepResearchConfig {
  // System limits
  maxConcurrentSessions: number;
  maxConcurrentAgents: number;
  defaultTimeout: number;
  maxTimeout: number;
  
  // Quality settings
  defaultQuality: DeepResearchOptions['researchQuality'];
  defaultDepth: DeepResearchOptions['analysisDepth'];
  
  // Model configuration
  models: {
    research: string;
    analysis: string;
    factCheck: string;
    synthesis: string;
    report: string;
  };
  
  // Tool configuration
  tools: ResearchTool[];
  
  // Performance settings
  enableCaching: boolean;
  cacheTimeout: number;
  enableParallelization: boolean;
  
  // Monitoring
  enableMetrics: boolean;
  enableDetailedLogging: boolean;
  
  // Rate limiting
  rateLimits: {
    perUser: number;
    perSession: number;
    global: number;
  };
}

/**
 * Research session state management
 */
export interface ResearchSession {
  id: string;
  userId?: string;
  query: string;
  options: DeepResearchOptions;
  
  // State
  status: ResearchSessionStatus;
  phase: ResearchPhase;
  progress: number;
  
  // Agents and tasks
  agents: IResearchAgent[];
  tasks: AgentTask[];
  
  // Results
  intermediateResults: any[];
  finalResult?: DeepResearchResult;
  
  // Timing
  startTime: Date;
  endTime?: Date;
  estimatedCompletion?: Date;
  
  // Error handling
  errors: ResearchError[];
  retryCount: number;
  
  // Metadata
  metadata: Record<string, any>;
}

/**
 * Research session status
 */
export enum ResearchSessionStatus {
  QUEUED = 'queued',
  INITIALIZING = 'initializing',
  RUNNING = 'running',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  TIMEOUT = 'timeout'
}

// Alias for backward compatibility
export type ResearchStatus = ResearchSessionStatus

// Missing type exports for tool definitions
export interface Tool {
  id: string
  name: string
  description: string
  execute: (params: any) => Promise<ToolResult>
}

export interface ToolResult {
  success: boolean
  data?: any
  error?: string
  metadata?: Record<string, any>
}

export interface ResearchSource {
  url: string
  title: string
  content?: string
  reliability?: number
  timestamp?: Date
  metadata?: Record<string, any>
  relevance?: number
  snippet?: string
}

// ===== STREAMING INTEGRATION TYPES =====

/**
 * Streaming event handler for real-time updates
 */
export interface StreamingEventHandler {
  onProgress?: (update: ResearchProgressUpdate) => void;
  onAgent?: (agent: IResearchAgent, task: AgentTask) => void;
  onResult?: (result: Partial<DeepResearchResult>) => void;
  onError?: (error: ResearchError) => void;
  onComplete?: (result: DeepResearchResult) => void;
}

/**
 * Streaming configuration options
 */
export interface StreamingConfig {
  enabled: boolean;
  updateInterval: number;       // Milliseconds between progress updates
  includeIntermediateResults: boolean;
  includeAgentStatus: boolean;
  includeMetrics: boolean;
  compression?: boolean;
}

// ===== UTILITY AND HELPER TYPES =====

/**
 * Research quality assessment
 */
export interface QualityAssessment {
  overall: number;              // 0-100 overall quality score
  sourceReliability: number;    // Reliability of sources used
  factualAccuracy: number;      // Fact checking confidence
  completeness: number;         // Coverage of the topic
  coherence: number;            // Logical flow and consistency
  timeliness: number;           // Recency of information
  depth: number;               // Thoroughness of analysis
}

/**
 * Cache management for research results
 */
export interface ResearchCache {
  key: string;
  query: string;
  result: DeepResearchResult;
  quality: QualityAssessment;
  createdAt: Date;
  expiresAt: Date;
  hits: number;
  lastAccessed: Date;
}

/**
 * Model selection criteria for different research tasks
 */
export interface ModelSelectionCriteria {
  task: AgentTaskType;
  requiredCapabilities: ToolCapability[];
  preferredProviders?: string[];
  maxCost?: number;
  maxLatency?: number;
  minQuality?: number;
  fallbackModels?: string[];
}

/**
 * Research analytics and insights
 */
export interface ResearchAnalytics {
  sessionId: string;
  totalQueries: number;
  averageQuality: number;
  averageLatency: number;
  successRate: number;
  topSources: string[];
  commonTopics: string[];
  userSatisfaction?: number;
  costEfficiency: number;
}

// ===== EXPORT ALL TYPES =====

export type {
  // Re-export core types for convenience
  SearchResult,
  User,
  Model,
  RouterDecision,
  AIMessage,
  ModelInfo
};