/**
 * Streaming Handler Module
 * Handles AI SDK v5 streaming configuration and response
 */

import { streamText, convertToModelMessages, smoothStream } from 'ai'
import { getAISDKModel } from '../model/mapper'
import { toUIMessages, toMultimodalMessages } from '../messages/converter'
import { ChatRequest } from '../request/types'
import { getModelConfig } from '../model/selector'
import { ModelConfiguration, generateSystemPromptAdditions } from '../features/advanced-model-handling'
import { PersonalizationResult } from '../personalization/types'
import { ConversationCompactionResult } from '../features/conversation-compaction'

export interface StreamConfig {
  model: any // AI SDK model instance
  messages: any[] // Model messages
  system?: string
  temperature?: number
  maxOutputTokens?: number
  topP?: number
  maxTokens?: number
  tools?: any
  onFinish?: (result: any) => void
  // Model identification
  modelId?: string // Original model ID for provider-specific configuration
  // Sophisticated features configuration
  modelConfiguration?: ModelConfiguration
  personalization?: PersonalizationResult
  compactionMetadata?: ConversationCompactionResult['metadata']
  // Model restriction metadata
  modelRestriction?: {
    wasRestricted: boolean
    originalModel?: string
    restrictionMessage?: string
    requiresUpgrade?: boolean
    suggestedPlan?: string
  }
}

/**
 * Create stream configuration
 */
export async function createStreamConfig(
  modelId: string,
  messages: ChatRequest['messages'],
  request: ChatRequest,
  options: {
    systemPrompt?: string
    modelConfiguration?: ModelConfiguration // Advanced model handling
    modelSelection?: any // Analysis data from model selector
    session?: any // User session for personalization
    personalization?: PersonalizationResult // User personalization data
    compactionMetadata?: ConversationCompactionResult['metadata'] // Compaction info
    modelRestriction?: StreamConfig['modelRestriction'] // Model restriction info
    onFinish?: (result: any) => void
    tools?: any
  } = {}
): Promise<StreamConfig> {
  console.log('🚀 [DEBUG] createStreamConfig called with attachments:', request.attachmentFiles?.length || 0)
  
  // Get model configuration
  const modelConfig = getModelConfig(modelId, request)
  
  // Validate input messages
  if (!messages || !Array.isArray(messages)) {
    console.error('[StreamConfig] Invalid messages:', messages)
    throw new Error('Messages must be a non-empty array')
  }
  
  // Convert messages to proper AI SDK v5 UIMessage format
  console.log('[StreamConfig] Converting messages, checking for attachments:', {
    messageCount: messages?.length || 0,
    messagesWithAttachments: messages?.filter((m: any) => m.attachments?.length > 0).length || 0,
    messagesWithParts: messages?.filter((m: any) => m.parts?.length > 0).length || 0
  })
  
  let uiMessages = Array.isArray(messages) ? messages.map(msg => {
    // Create base UIMessage
    const uiMessage: any = {
      id: msg.id || crypto.randomUUID(),
      role: msg.role as 'user' | 'assistant' | 'system',
      createdAt: msg.createdAt || new Date(),
    }
    
    // Check if message already has parts array (AI SDK v5 format from frontend)
    if (msg.parts && Array.isArray(msg.parts)) {
      console.log('[StreamConfig] Message already has parts array, preserving it:', {
        role: msg.role,
        partsCount: msg.parts.length,
        partTypes: msg.parts.map((p: any) => p.type)
      })
      uiMessage.parts = msg.parts
    } else {
      // Convert content to proper AI SDK v5 UIMessage format
      const messageContent = typeof msg.content === 'string' ? msg.content : msg.content || ''
      
      // AI SDK v5 UIMessage: use 'parts' array NOT 'content' array
      // Based on official docs: UIMessage uses parts array for multimodal support
      uiMessage.parts = messageContent ? [{ type: 'text', text: messageContent }] : []
      
      // IMPORTANT: Reconstruct parts from attachments if they exist (for messages loaded from DB)
      // Apply sliding window approach - only include attachments from recent messages
      const messageIndex = messages.indexOf(msg)
      const messagesFromEnd = messages.length - messageIndex
      const shouldIncludeAttachments = messagesFromEnd <= 5 // Include attachments from last 5 messages
      
      if ((msg as any).attachments && Array.isArray((msg as any).attachments) && (msg as any).attachments.length > 0 && shouldIncludeAttachments) {
        console.log('[StreamConfig] Reconstructing parts from attachments:', {
          role: msg.role,
          messageIndex,
          messagesFromEnd,
          attachmentCount: (msg as any).attachments?.length || 0,
          attachmentTypes: (msg as any).attachments?.map((a: any) => a.type) || []
        })
        
        // Add attachment parts to the parts array
        const attachments = (msg as any).attachments || [];
        for (const attachment of attachments) {
          if (attachment.type === 'image' && attachment.url) {
            // Check if it's base64 data or a regular URL
            let imageSrc = attachment.url
            
            // If it's a large string without data: prefix, it's likely base64
            if (imageSrc.length > 1000 && !imageSrc.startsWith('data:') && !imageSrc.startsWith('http')) {
              const mimeType = attachment.mimeType || 'image/png'
              imageSrc = `data:${mimeType};base64,${imageSrc}`
            }
            
            uiMessage.parts.push({
              type: 'image' as const,
              image: imageSrc
            })
          } else if ((attachment.type === 'document' || attachment.type === 'file') && attachment.url) {
            // Handle non-image files
            let fileData = attachment.url
            
            // Add data URL prefix if needed
            if (!fileData.startsWith('data:') && !fileData.startsWith('http')) {
              const mimeType = attachment.mimeType || 'application/octet-stream'
              fileData = `data:${mimeType};base64,${fileData}`
            }
            
            uiMessage.parts.push({
              type: 'file' as const,
              data: fileData,
              mediaType: attachment.mimeType || 'application/octet-stream',
              filename: attachment.name || 'file'
            })
          }
        }
      }
    }
    
    return uiMessage
  }) : []
  
  console.log('[StreamConfig] Initial AI SDK v5 UIMessages created:', {
    originalMessagesCount: messages?.length || 0,
    uiMessagesCount: uiMessages.length,
    isArray: Array.isArray(uiMessages),
    format: 'AI SDK v5 with parts array'
  })
  
  // Check if there are older attachments not included due to sliding window
  const olderMessagesWithAttachments = messages.slice(0, -5).filter((msg: any) => 
    msg.attachments?.length > 0
  ).length
  
  if (olderMessagesWithAttachments > 0) {
    console.log('[StreamConfig] Older attachments exist but not included:', {
      olderMessagesWithAttachments,
      note: 'Adding system message to inform AI'
    })
    
    // Add a system message at the beginning to inform about older attachments
    uiMessages.unshift({
      id: crypto.randomUUID(),
      role: 'system',
      createdAt: new Date(),
      parts: [{
        type: 'text',
        text: `Note: This conversation contains ${olderMessagesWithAttachments} earlier message(s) with attachments (images/files) that are not included in the current context to optimize performance. If the user references "the image above" or similar, they may be referring to attachments from earlier in the conversation.`
      }]
    })
  }
  
  // Handle V5 attachments - add file parts to the last user message using proper AI SDK v5 format
  if (request.attachmentFiles?.length) {
    console.log('[StreamConfig] Processing V5 attachments for AI SDK v5 parts array:', request.attachmentFiles.length)
    
    const lastUserMessageIndex = uiMessages.map(m => m.role).lastIndexOf('user')
    
    if (lastUserMessageIndex !== -1) {
      // Convert V5AttachmentFile to proper AI SDK v5 parts format
      // Based on official AI SDK v5 documentation - images use 'data' property with data URL format
      const fileParts = request.attachmentFiles.map(file => {
        const isImage = file.type.startsWith('image/')
        
        if (isImage) {
          // AI SDK v5 image parts for UIMessage use 'image' property with data URL format
          // This will be converted properly by convertToModelMessages()
          const dataUrl = `data:${file.type};base64,${file.data}`
          
          console.log('[StreamConfig] Creating AI SDK v5 image part:', {
            modelId,
            mediaType: file.type,
            fileName: file.name,
            dataLength: file.data.length,
            dataUrlPreview: dataUrl.substring(0, 70) + '...'
          })
          
          // AI SDK v5 UIMessage format: { type: 'image', image: dataUrl }
          return {
            type: 'image' as const,
            image: dataUrl
          }
        } else {
          // Non-image files use 'file' type with proper AI SDK v5 format
          return {
            type: 'file' as const,
            data: `data:${file.type};base64,${file.data}`,
            mediaType: file.type,
            filename: file.name
          }
        }
      })
      
      // Add file parts to the UIMessage parts array (AI SDK v5 format)
      // AI SDK v5 expects parts to be an array of parts for multimodal messages
      uiMessages[lastUserMessageIndex].parts = [
        ...uiMessages[lastUserMessageIndex].parts,
        ...fileParts
      ]
      
      console.log('[StreamConfig] Added V5 file parts to UIMessage:', {
        attachmentCount: fileParts.length,
        totalParts: uiMessages[lastUserMessageIndex].parts.length,
        parts: uiMessages[lastUserMessageIndex].parts.map((p: any) => ({ 
          type: p.type, 
          ...(p.type === 'image' && { hasImageData: !!p.image, dataLength: p.image?.length }),
          ...(p.type === 'file' && { hasFile: true, filename: p.filename, mediaType: p.mediaType })
        }))
      })
    }
  }
  
  // Handle legacy multimodal messages if needed
  if (request.attachments?.length || request.attachmentData?.length) {
    uiMessages = toMultimodalMessages(
      uiMessages,
      request.attachments,
      request.attachmentData
    )
  }
  
  // Filter out invalid messages - check parts array (AI SDK v5 format)
  if (Array.isArray(uiMessages)) {
    uiMessages = uiMessages.filter(msg => {
      // System messages are always valid
      if (msg.role === 'system') {
        return true
      }
      
      // Must have valid parts array
      if (!msg.parts || !Array.isArray(msg.parts)) {
        console.warn('[StreamConfig] Message missing parts array, skipping:', {
          role: msg.role,
          id: msg.id
        })
        return false
      }
      
      // CRITICAL: Reject messages with empty parts arrays - Google Gemini API fails on these
      if (msg.parts.length === 0) {
        console.warn('[StreamConfig] Filtering out message with empty parts array:', {
          role: msg.role,
          id: msg.id
        })
        return false
      }
      
      // For all messages, check if at least one part has content
      const hasValidPart = msg.parts.some((part: any) => {
        if (part.type === 'text' && part.text !== undefined) return true
        if (part.type === 'file' && (part.data || part.url)) return true  
        if (part.type === 'image' && part.image) return true
        return false
      })
      
      if (!hasValidPart) {
        console.warn('[StreamConfig] Filtering out message with no valid part content:', {
          role: msg.role,
          id: msg.id,
          parts: msg.parts
        })
        return false
      }
      
      return true
    })
  } else {
    console.error('[StreamConfig] uiMessages is not an array:', typeof uiMessages, uiMessages)
    throw new Error('UI messages must be an array')
  }
  
  console.log('[StreamConfig] AI SDK v5 UI Messages to convert:', {
    count: uiMessages.length,
    hasFiles: uiMessages.some(m => m.parts?.some((p: any) => p.type === 'file')),
    hasImages: uiMessages.some(m => m.parts?.some((p: any) => p.type === 'image')),
    fileCount: uiMessages.reduce((count, m) => count + (m.parts?.filter((p: any) => p.type === 'file').length || 0), 0),
    imageCount: uiMessages.reduce((count, m) => count + (m.parts?.filter((p: any) => p.type === 'image').length || 0), 0),
    totalParts: uiMessages.reduce((count, m) => count + (m.parts?.length || 0), 0)
  })
  
  // Convert UIMessages to ModelMessages for AI SDK v5 (this handles file parts automatically)
  console.log('[StreamConfig] About to convert AI SDK v5 UIMessages:', {
    count: uiMessages.length,
    modelId: modelId,
    isGoogleModel: modelId.includes('google') || modelId.includes('gemini'),
    messages: uiMessages.map((msg: any, i: number) => ({
      index: i,
      id: msg.id,
      role: msg.role,
      partsCount: msg.parts?.length || 0,
      parts: msg.parts?.map((p: any) => ({ 
        type: p.type, 
        ...(p.type === 'image' && { hasImageProp: !!p.image, dataLength: p.image?.length }),
        ...(p.type === 'file' && { hasUrlProp: !!p.url, filename: p.name }),
        ...(p.type === 'text' && { hasText: !!p.text, textLength: p.text?.length })
      })) || [],
      createdAt: msg.createdAt
    })),
    convertToModelMessagesType: typeof convertToModelMessages
  })
  
  // CRITICAL DEBUG: Log the FULL UIMessage structure before conversion
  console.log('[StreamConfig] FULL UIMessage before convertToModelMessages:', JSON.stringify(uiMessages, null, 2))
  
  // ADDITIONAL DEBUG: Log parts array structure specifically
  uiMessages.forEach((msg, i) => {
    console.log(`[StreamConfig] UIMessage[${i}] parts analysis:`, {
      id: msg.id,
      role: msg.role,
      hasPartsArray: !!msg.parts,
      partsIsArray: Array.isArray(msg.parts),
      partsLength: msg.parts?.length || 0,
      partsDetails: msg.parts?.map((part: any, j: number) => ({
        index: j,
        type: part.type,
        hasText: part.type === 'text' && !!part.text,
        hasImage: part.type === 'image' && !!part.image,
        imagePreview: part.type === 'image' && part.image ? part.image.substring(0, 50) + '...' : undefined
      })) || []
    })
  })
  
  // Log image data for debugging
  const hasImages = uiMessages.some((m: any) => m.parts?.some((p: any) => p.type === 'image'))
  if (hasImages) {
    console.log('[StreamConfig] Messages contain images:', {
      modelId,
      imageCount: uiMessages.reduce((count, msg) => 
        count + (msg.parts?.filter((p: any) => p.type === 'image').length || 0), 0
      )
    })
  }
  
  // For AI SDK v5 with multimodal content, ALWAYS use convertToModelMessages
  // The AI SDK knows how to properly convert UIMessage format to ModelMessage format
  const hasMultimodalContent = uiMessages.some((m: any) => 
    m.parts?.some((p: any) => p.type === 'image' || p.type === 'file')
  )
  
  let modelMessages;
  
  console.log('[StreamConfig] Using convertToModelMessages for proper format conversion:', {
    hasMultimodalContent,
    totalMessages: uiMessages.length
  })
  
  try {
    // ALWAYS use convertToModelMessages - it handles both text-only and multimodal correctly
    modelMessages = convertToModelMessages(uiMessages)
    
    console.log('[StreamConfig] Model Messages after conversion:', {
      count: modelMessages.length,
      modelId: modelId,
      isGoogleModel: modelId.includes('google') || modelId.includes('gemini'),
      hasMultipartContent: modelMessages.some(m => Array.isArray(m.content)),
      messages: modelMessages.map((msg: any, i: number) => ({
        index: i,
        role: msg.role,
        contentType: Array.isArray(msg.content) ? 'array' : typeof msg.content,
        ...(Array.isArray(msg.content) && {
          parts: msg.content.map((part: any) => ({
            type: part.type,
            ...(part.type === 'image' && { hasImage: !!part.image, imageType: typeof part.image }),
            ...(part.type === 'file' && { hasData: !!part.data, dataType: typeof part.data, mediaType: part.mediaType })
          }))
        })
      })),
      lastMessage: JSON.stringify(modelMessages[modelMessages.length - 1], null, 2)
    })
      
  } catch (error) {
    console.error('[StreamConfig] Error converting to model messages:', error)
    console.error('[StreamConfig] UIMessages that caused error:', JSON.stringify(uiMessages, null, 2))
    throw error
  }
  
  // Get AI SDK model instance with error handling
  let aiModel
  try {
    aiModel = getAISDKModel(modelId)
    console.log(`[StreamConfig] Successfully mapped model: ${modelId}`)
  } catch (error) {
    console.error(`[StreamConfig] Failed to map model ${modelId}:`, error)
    
    // For AI SDK v5 compatibility issues, try a fallback
    if ((error as any)?.message?.includes('specification version') || (error as any)?.message?.includes('Unsupported model version')) {
      console.warn(`[StreamConfig] AI SDK v5 compatibility issue, using fallback model`)
      aiModel = getAISDKModel('openai/gpt-4o-mini')
    } else {
      throw error
    }
  }
  
  // Generate sophisticated system prompt with all features
  const systemPrompt = options.systemPrompt || 
    await generateSophisticatedSystemPrompt(
      modelId, 
      request, 
      options.modelConfiguration,
      options.modelSelection?.analysisResult, 
      options.session,
      options.personalization,
      options.compactionMetadata
    )
  
  // Use advanced model configuration if available
  const temperature = options.modelConfiguration?.temperature ?? modelConfig.temperature
  const maxOutputTokens = options.modelConfiguration?.maxOutputTokens ?? modelConfig.maxOutputTokens
  const topP = options.modelConfiguration?.topP
  
  console.log('[StreamConfig] Using sophisticated configuration:', {
    temperature,
    maxOutputTokens,
    topP,
    modelType: options.modelConfiguration?.modelType,
    hasPersonalization: options.personalization?.hasPersonalization,
    compactionRatio: options.compactionMetadata?.compressionRatio
  })
  
  return {
    model: aiModel,
    messages: modelMessages,
    system: systemPrompt,
    temperature: temperature,
    maxOutputTokens: maxOutputTokens,
    ...(topP !== undefined && { topP }),
    tools: options.tools,
    onFinish: options.onFinish,
    // Pass model identification for provider-specific configuration
    modelId: modelId,
    // Pass sophisticated features metadata
    modelConfiguration: options.modelConfiguration,
    personalization: options.personalization,
    compactionMetadata: options.compactionMetadata,
    modelRestriction: options.modelRestriction
  }
}

/**
 * Stream chat response with persistence support
 */
export async function streamChatResponse(config: StreamConfig) {
  console.log('[Streaming] Starting AI SDK v5 stream with persistence...')
  
  // Generate stream ID for tracking
  const streamId = `stream_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  const streamStartTime = Date.now()
  
  // Track stream state for persistence
  const streamState = {
    id: streamId,
    content: '',
    usage: null as any,
    finishReason: null as any,
    completed: false,
  }
  
  try {
    // Get the model ID from the stream config (passed from createStreamConfig)
    const actualModelId = config.modelId || config.modelConfiguration?.modelId || 'unknown'
    
    // Check if this is an OpenAI reasoning model based on documentation
    // o3-mini uses Chat API but still supports reasoning with providerOptions
    // o3, o4-mini, o1 use Responses API with reasoning
    const isOpenAIReasoningModel = actualModelId.includes('o3') || actualModelId.includes('o4') || actualModelId.includes('o1')
    
    // Check if this is a Google thinking model
    // Enable thinking for models with '-thinking' suffix OR models that natively support it
    const isGoogleThinkingModel = actualModelId.includes('gemini') && 
      (actualModelId.includes('-thinking') ||  // Models with 'thinking' suffix
       actualModelId.includes('gemini-2.5-pro') || // Gemini 2.5 Pro supports thinking natively
       actualModelId.includes('gemini-2.0-flash-thinking')) // Explicit thinking model
    
    // Check if this is a Claude thinking model
    // Claude models with '-thinking' suffix in the database need special handling
    const isClaudeThinkingModel = actualModelId.includes('anthropic/claude') && actualModelId.includes('-thinking')
    
    // Check if this is a Groq reasoning model (QWen-QWQ, DeepSeek R1, -reasoning suffix)
    // NOTE: Kimi K2 excluded - it doesn't support reasoningFormat parameter
    const isGroqReasoningModel = actualModelId.includes('groq') && 
      (actualModelId.includes('qwen-qwq') || 
       actualModelId.includes('deepseek-r1') || 
       actualModelId.includes('-reasoning')) &&
      !actualModelId.includes('kimi-k2') // Explicitly exclude Kimi K2
    
    // Check if this is an xAI reasoning model (Grok with reasoning)
    const isXaiReasoningModel = actualModelId.includes('xai') && 
      actualModelId.includes('-reasoning')
    
    console.log('[Streaming] Model analysis for reasoning configuration:', {
      actualModelId,
      configModelId: config.modelId,
      fallbackModelId: config.modelConfiguration?.modelId,
      isOpenAIReasoningModel,
      isGoogleThinkingModel,
      isClaudeThinkingModel,
      isGroqReasoningModel,
      isXaiReasoningModel,
      willConfigureReasoning: isOpenAIReasoningModel || isGoogleThinkingModel || isClaudeThinkingModel || isGroqReasoningModel || isXaiReasoningModel
    })
    
    // Configure provider options for reasoning models
    const providerOptions: any = {}
    
    // OpenAI reasoning models configuration
    if (isOpenAIReasoningModel) {
      providerOptions.openai = {
        reasoningEffort: 'high', // Maximum reasoning for best results  
        reasoningSummary: 'auto' // Auto summary level for reasoning content
      }
      console.log('[Streaming] Configured OpenAI reasoning options:', providerOptions.openai)
    }
    
    // Google thinking models configuration based on official AI SDK v5 docs
    if (isGoogleThinkingModel) {
      providerOptions.google = {
        thinkingConfig: {
          thinkingBudget: 24576,  // Maximum thinking budget for best results
          includeThoughts: true   // Return thought summaries in the reasoning field
          // Note: Setting thinkingBudget to 0 disables thinking, values 1-1024 are set to 1024
        }
      }
      console.log('[Streaming] Configured Google thinking options:', providerOptions.google)
    }
    
    // Claude thinking models configuration based on official AI SDK v5 docs
    if (isClaudeThinkingModel) {
      providerOptions.anthropic = {
        thinking: {
          type: 'enabled' as const,
          budgetTokens: 12000 // High budget for best results
        }
      }
      console.log('[Streaming] Configured Claude thinking options:', providerOptions.anthropic)
    }
    
    // Groq reasoning models configuration - SIMPLE FLAG for fast reasoning
    // IMPORTANT: Kimi K2 is explicitly excluded from reasoning configuration to avoid chunk type errors
    if (isGroqReasoningModel && !actualModelId.includes('kimi-k2')) {
      providerOptions.groq = {
        reasoningFormat: 'parsed' // Simple flag for parsed reasoning output
        // Options: 'parsed' | 'raw' | 'hidden'
      }
      console.log('[Streaming] Configured Groq reasoning options:', providerOptions.groq)
    } else if (actualModelId.includes('kimi-k2')) {
      console.log('[Streaming] Kimi K2 detected - using simple streaming without provider options to avoid chunk errors')
    }
    
    // xAI reasoning models configuration - SIMPLE FLAG for reasoning effort
    if (isXaiReasoningModel) {
      providerOptions.xai = {
        reasoningEffort: 'high' // Simple flag for high reasoning effort
        // Options: 'low' | 'high'
      }
      console.log('[Streaming] Configured xAI reasoning options:', providerOptions.xai)
    }
    
    // Special handling for Kimi K2 - use simple streaming without any transformations
    const isKimiK2Model = actualModelId.includes('kimi-k2')
    
    // Apply smooth streaming transformation for OpenRouter models to fix buffering issues
    // NOTE: Completely disable any stream transforms for Kimi K2 to avoid chunk processing errors
    const isOpenRouterModel = config.modelId?.includes('openrouter/')
    const streamTransforms = (isOpenRouterModel && !isKimiK2Model) ? [smoothStream({
      delayInMs: 15, // Small delay for smooth streaming
      chunking: 'word' // Stream word by word instead of in large chunks
    })] : undefined

    console.log('[Streaming] Model streaming configuration:', {
      modelId: config.modelId,
      actualModelId,
      isKimiK2: isKimiK2Model,
      isOpenRouter: isOpenRouterModel,
      transformsEnabled: !!streamTransforms,
      transformCount: streamTransforms?.length || 0,
      hasProviderOptions: Object.keys(providerOptions).length > 0
    })

    console.log('[Streaming] About to call streamText with config:', {
      modelType: typeof config.model,
      hasSystem: !!config.system,
      hasTemperature: config.temperature !== undefined,
      hasMaxOutputTokens: !!config.maxOutputTokens,
      hasTools: !!config.tools,
      hasProviderOptions: Object.keys(providerOptions).length > 0,
      hasTransforms: !!streamTransforms,
      providerOptions: JSON.stringify(providerOptions)
    })

    const result = await streamText({
      model: config.model,
      messages: config.messages,
      ...(config.system && { system: config.system }),
      ...(config.temperature !== undefined && { temperature: config.temperature }),
      ...(config.topP !== undefined && { topP: config.topP }),
      ...(config.maxOutputTokens && { maxOutputTokens: config.maxOutputTokens }),
      ...(config.tools && { tools: config.tools }),
      ...(Object.keys(providerOptions).length > 0 && { providerOptions }),
      ...(streamTransforms && { experimental_transform: streamTransforms }),
      
      // Enhanced onFinish that ensures persistence and sophisticated features tracking
      onFinish: async ({ text, usage, finishReason }) => {
        console.log('[Streaming] Sophisticated stream finished:', {
          streamId,
          duration: `${Date.now() - streamStartTime}ms`,
          textLength: text?.length,
          usage,
          finishReason,
          modelType: config.modelConfiguration?.modelType,
          hadPersonalization: config.personalization?.hasPersonalization,
          compactionRatio: config.compactionMetadata?.compressionRatio,
          features: 'advanced-model-handling + personalization + conversation-compaction'
        })
        
        // Update stream state
        streamState.content = text || ''
        streamState.usage = usage
        streamState.finishReason = finishReason
        streamState.completed = true
        
        // Call original onFinish if provided
        if (config.onFinish) {
          try {
            await config.onFinish({
              text,
              usage,
              finishReason,
              reasoning: undefined, // AI SDK v5 doesn't provide reasoning in onFinish
              messages: undefined, // AI SDK v5 doesn't provide messages in onFinish
              // Pass sophisticated features metadata
              modelConfiguration: config.modelConfiguration,
              personalization: config.personalization,
              compactionMetadata: config.compactionMetadata
            })
          } catch (error) {
            console.error('[Streaming] Error in sophisticated onFinish callback:', error)
            // Don't throw - we still want to save the message
          }
        }
        
        // Ensure message is persisted even if client disconnected
        // This happens in the background and doesn't block the response
        persistStreamResult(streamId, streamState).catch(error => {
          console.error('[Streaming] Failed to persist stream result:', error)
        })
      },
    })
    
    // Add stream ID to response headers for client tracking
    // Note: AI SDK v5 returns a Promise, not an object with headers
    // Headers should be set on the final Response object instead
    
    return result
  } catch (error) {
    console.error('[Streaming] Sophisticated stream error:', error, {
      modelType: config.modelConfiguration?.modelType,
      streamingMode: config.modelConfiguration?.streamingMode,
      modelId: config.modelConfiguration?.modelId
    })
    
    // Attempt to save partial result on error
    if (streamState.content) {
      persistStreamResult(streamId, {
        ...streamState,
        finishReason: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        modelConfiguration: config.modelConfiguration,
        personalization: config.personalization,
        compactionMetadata: config.compactionMetadata
      }).catch(console.error)
    }
    
    throw error
  }
}

/**
 * Persist stream result to ensure it's saved
 */
async function persistStreamResult(streamId: string, state: any) {
  try {
    // In production, this would save to database or cache
    // For now, just log the completion
    console.log('[Streaming] Stream result ready for persistence:', {
      streamId,
      contentLength: state.content?.length,
      completed: state.completed,
      finishReason: state.finishReason
    })
    
    // TODO: Implement actual persistence to database
    // This would save the message to the conversation in the database
  } catch (error) {
    console.error('[Streaming] Failed to persist stream result:', error)
  }
}

/**
 * Generate sophisticated system prompt integrating all features:
 * - Advanced model handling
 * - User personalization 
 * - Conversation compaction context
 * - Intelligent router analysis
 */
async function generateSophisticatedSystemPrompt(
  modelId: string, 
  request: ChatRequest,
  modelConfiguration?: ModelConfiguration,
  analysisResult?: any,
  session?: any,
  personalization?: PersonalizationResult,
  compactionMetadata?: ConversationCompactionResult['metadata']
): Promise<string> {
  // Use our sophisticated system prompt generator with all features
  const { generateSophisticatedSystemPrompt } = await import('../prompts/sophisticated-system')
  
  // Map analysis result to router analysis format
  const routerAnalysis = analysisResult ? {
    primary_category: analysisResult.primary_category,
    complexity: analysisResult.complexity,
    confidence: analysisResult.confidence,
    reasoning: analysisResult.reasoning
  } : undefined
  
  // Generate base sophisticated system prompt with personalization
  const basePrompt = await generateSophisticatedSystemPrompt(
    modelId,
    request,
    session,
    routerAnalysis
  )
  
  // Add model-specific optimizations from advanced model handling
  let enhancedPrompt = basePrompt
  if (modelConfiguration) {
    const modelAdditions = generateSystemPromptAdditions(modelConfiguration)
    if (modelAdditions) {
      enhancedPrompt += '\n\n' + modelAdditions
    }
  }
  
  // Add personalization instructions if available
  if (personalization?.personalizationInstructions) {
    enhancedPrompt += '\n\n### USER PERSONALIZATION\n' + personalization.personalizationInstructions
  }
  
  // Add conversation context information if compacted
  if (compactionMetadata && compactionMetadata.compressionRatio < 0.9) {
    enhancedPrompt += `\n\n### CONVERSATION CONTEXT\nThis conversation has been optimized for your processing. Original messages: ${compactionMetadata.originalCount}, Current: ${compactionMetadata.compactedCount}. Key context has been preserved to maintain coherence.`
  }
  
  // Add greeting if available
  if (personalization?.userGreeting) {
    enhancedPrompt += '\n\n### USER GREETING\nStart your response with: ' + personalization.userGreeting
  }
  
  console.log('[SystemPrompt] Generated sophisticated prompt:', {
    baseLength: basePrompt.length,
    enhancedLength: enhancedPrompt.length,
    hasModelOptimizations: !!modelConfiguration,
    hasPersonalization: !!personalization?.personalizationInstructions,
    hasCompactionContext: !!(compactionMetadata && compactionMetadata.compressionRatio < 0.9),
    hasGreeting: !!personalization?.userGreeting
  })
  
  return enhancedPrompt
}

/**
 * Create error stream response
 */
export function createErrorStream(error: any) {
  const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
  
  // Create a simple text stream with the error
  return new Response(
    new ReadableStream({
      start(controller) {
        controller.enqueue(new TextEncoder().encode(`Error: ${errorMessage}`))
        controller.close()
      }
    }),
    {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      }
    }
  )
}