import { z } from 'zod';
import { tool } from 'ai';
import type { ResearchSource, ToolResult } from '../types';

// Brave Search API response types
interface BraveSearchResult {
  title: string;
  url: string;
  description: string;
  age?: string;
  published?: string;
  thumbnail?: string;
  extra_snippets?: string[];
}

interface BraveSearchResponse {
  query: {
    original: string;
    show_strict_warning: boolean;
    is_navigational: boolean;
    local_decision: string;
    local_locations_idx: number;
    is_news_breaking: boolean;
    spellcheck_off: boolean;
    country: string;
    bad_results: boolean;
    should_fallback: boolean;
    postal_code: string;
    city: string;
    header_country: string;
    more_results_available: boolean;
    state: string;
  };
  mixed: {
    type: string;
    main: Array<{ type: string; index: number; all: boolean }>;
    top: any[];
    side: any[];
  };
  type: string;
  web?: {
    type: string;
    results: BraveSearchResult[];
    family_friendly: boolean;
  };
  news?: {
    type: string;
    results: BraveSearchResult[];
  };
}

// Brave Search configuration
const BRAVE_SEARCH_API_KEY = process.env.BRAVE_SEARCH_API_KEY || '';
const BRAVE_SEARCH_API_URL = 'https://api.search.brave.com/res/v1/web/search';

// Search parameters schema based on Brave Search API documentation
const braveSearchSchema = z.object({
  q: z.string().min(1).max(400).describe('The search query (required, max 400 chars)'),
  count: z.number().int().min(1).max(20).optional().describe('Number of results to return (max 20)'),
  country: z.string().length(2).optional().describe('2-character country code (default: US)'),
  safesearch: z.enum(['off', 'moderate', 'strict']).optional().describe('Safe search filter level'),
  freshness: z.enum(['pd', 'pw', 'pm', 'py']).optional().describe('Time range filter'),
  offset: z.number().int().min(0).max(9).optional().describe('Pagination offset (max 9)'),
  search_lang: z.string().optional().describe('Search language preference'),
  result_filter: z.string().optional().describe('Comma-delimited result types to include')
});

export type BraveSearchParams = z.infer<typeof braveSearchSchema>;

/**
 * Brave Search Tool for web research
 * Provides access to Brave's privacy-focused search engine with AI-enhanced results
 */
export const braveSearchTool = tool({
  description: 'Search the web using Brave Search API for current information, news, and research',
  parameters: braveSearchSchema,
  execute: async (params: any): Promise<ToolResult> => {
    try {
      if (!BRAVE_SEARCH_API_KEY) {
        throw new Error('Brave Search API key not configured');
      }

      // Apply defaults to parameters based on Brave API documentation
      const {
        q,
        count = 10,
        country = 'US',
        safesearch = 'moderate',
        freshness,
        offset,
        search_lang,
        result_filter
      } = params;

      // Build query parameters
      const searchParams = new URLSearchParams({
        q,
        count: count.toString(),
        country,
        safesearch,
        ...(freshness && { freshness }),
        ...(offset !== undefined && { offset: offset.toString() }),
        ...(search_lang && { search_lang }),
        ...(result_filter && { result_filter })
      });

      // Make API request
      const response = await fetch(`${BRAVE_SEARCH_API_URL}?${searchParams}`, {
        headers: {
          'Accept': 'application/json',
          'Accept-Encoding': 'gzip',
          'X-Subscription-Token': BRAVE_SEARCH_API_KEY
        }
      });

      if (!response.ok) {
        throw new Error(`Brave Search API error: ${response.status} ${response.statusText}`);
      }

      const data: BraveSearchResponse = await response.json();

      // Process results
      const results = params.news_only ? data.news?.results : data.web?.results;
      if (!results || results.length === 0) {
        return {
          success: false,
          error: 'No results found',
          data: null
        };
      }

      // Transform to ResearchSource format
      const sources: ResearchSource[] = results.map((result, index) => ({
        url: result.url,
        title: result.title,
        snippet: result.description,
        relevance: 1 - (index * 0.05), // Decrease relevance by rank
        metadata: {
          age: result.age,
          published: result.published,
          thumbnail: result.thumbnail,
          extra_snippets: result.extra_snippets
        }
      }));

      return {
        success: true,
        data: {
          sources,
          metadata: {
            query: data.query.original,
            total_results: results.length,
            more_available: data.query.more_results_available,
            search_type: params.news_only ? 'news' : 'web'
          }
        }
      };
    } catch (error) {
      console.error('[BraveSearch] Error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        data: null
      };
    }
  }
});

/**
 * Enhanced Brave Search with multiple queries and result aggregation
 */
export async function comprehensiveBraveSearch(
  queries: string[],
  options?: Partial<BraveSearchParams>
): Promise<ResearchSource[]> {
  const allSources = new Map<string, ResearchSource>();
  
  // Execute searches in parallel
  const searchPromises = queries.map(query => 
    braveSearchTool.execute({
      q: query,
      count: 10,
      ...options
    }, { 
      toolCallId: 'brave-search-' + Math.random().toString(36).substr(2, 9),
      messages: []
    })
  );

  const results = await Promise.allSettled(searchPromises);

  // Aggregate results
  for (const result of results) {
    if (result.status === 'fulfilled' && result.value.success && result.value.data?.sources) {
      for (const source of result.value.data.sources) {
        // Deduplicate by URL
        if (!allSources.has(source.url)) {
          allSources.set(source.url, source);
        } else {
          // Update relevance if higher
          const existing = allSources.get(source.url)!;
          if ((source.relevance || 0) > (existing.relevance || 0)) {
            allSources.set(source.url, source);
          }
        }
      }
    }
  }

  // Sort by relevance and return
  return Array.from(allSources.values())
    .sort((a, b) => (b.relevance || 0) - (a.relevance || 0))
    .slice(0, 20); // Limit to top 20 results
}

/**
 * Search for recent news articles using Brave Search
 */
export async function searchBraveNews(
  topic: string,
  freshness: 'pd' | 'pw' | 'pm' = 'pw'
): Promise<ResearchSource[]> {
  const result = await braveSearchTool.execute({
    q: topic,
    count: 15,
    freshness,
    result_filter: 'news',
    safesearch: 'moderate'
  }, { 
    toolCallId: 'brave-news-' + Math.random().toString(36).substr(2, 9),
    messages: []
  });

  if (result.success && result.data?.sources) {
    return result.data.sources;
  }
  return [];
}

/**
 * Fact-check a claim using Brave Search
 */
export async function factCheckWithBrave(
  claim: string
): Promise<{
  sources: ResearchSource[];
  credibility_indicators: string[];
}> {
  // Search for fact-checking related to the claim
  const factCheckQueries = [
    `"${claim}" fact check`,
    `"${claim}" debunked OR verified`,
    `"${claim}" snopes OR politifact OR factcheck.org`
  ];

  const sources = await comprehensiveBraveSearch(factCheckQueries, {
    freshness: 'py', // Past year for fact checks
    safesearch: 'strict'
  });

  // Extract credibility indicators from sources
  const credibility_indicators: string[] = [];
  
  for (const source of sources) {
    const lowerTitle = source.title.toLowerCase();
    const lowerSnippet = (source.snippet || '').toLowerCase();
    
    if (lowerTitle.includes('fact check') || lowerSnippet.includes('fact check')) {
      credibility_indicators.push('Found fact-checking sources');
    }
    if (lowerTitle.includes('debunked') || lowerSnippet.includes('debunked')) {
      credibility_indicators.push('Claim may be debunked');
    }
    if (lowerTitle.includes('verified') || lowerSnippet.includes('verified')) {
      credibility_indicators.push('Claim may be verified');
    }
  }

  return {
    sources,
    credibility_indicators: [...new Set(credibility_indicators)] // Remove duplicates
  };
}