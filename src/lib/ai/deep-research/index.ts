/**
 * Deep Research Module - Main Export
 * 
 * @description
 * Central export point for the Deep Research multi-agent system.
 * Provides a clean API surface for integration with JustSimpleChat
 * and external applications requiring sophisticated research capabilities.
 * 
 * @module lib/ai/deep-research
 * @version 1.0.0
 * <AUTHOR> Team
 */

// ===== IMPORTS FOR LOCAL USE =====

import type {
  DeepResearchRequest,
  DeepResearchOptions,
  DeepResearchResult,
  ResearchContext,
  StreamingEventHandler,
  ResearchSession
} from './types';

// ===== CORE EXPORTS =====

// Main orchestrator and supervisor classes
export { DeepResearchOrchestrator } from './orchestrator';
export { ResearchSupervisor } from './supervisor';

// ===== TYPE EXPORTS =====

// Core research types
export type {
  DeepResearchRequest,
  DeepResearchOptions,
  DeepResearchResult,
  ResearchContext,
  ResearchProgressUpdate,
  ResearchStreamChunk,
} from './types';

// Agent system types
export type {
  IResearchAgent,
  AgentTask,
  AgentTaskResult,
  AgentMetrics,
  AgentParameters,
} from './types';

// Enumerations
export {
  AgentType,
  AgentStatus,
  AgentCapability,
  AgentTaskType,
  TaskPriority,
  ResearchProgressType,
  ResearchPhase,
  ResearchSessionStatus,
  FactCheckStatus,
  SourceType,
  FindingCategory,
  EvidenceStrength,
  CitationFormat,
  ResearchToolType,
  ToolCapability,
} from './types';

// Enhanced result types
export type {
  EnhancedSearchResult,
  ResearchFinding,
  Evidence,
  Citation,
  AgentUsageInfo,
  QualityAssessment,
  ResearchAnalytics,
} from './types';

// Error types
export {
  ResearchError,
  AgentError,
  ResearchTimeoutError,
} from './types';

// ===== CONFIGURATION EXPORTS =====

export {
  deepResearchConfig,
  getDeepResearchConfig,
  isDeepResearchEnabled,
  isToolConfigured,
  getRateLimitsForPlan,
  validateDeepResearchConfig,
  reloadDeepResearchConfig,
} from './config';

export type {
  DeepResearchConfigType,
  SystemLimitsConfig,
  ModelConfig,
  ToolConfig,
  QualityConfig,
  RateLimitConfig,
  MonitoringConfig,
} from './config';

// ===== STREAMING EXPORTS =====

export { ProgressTracker } from './streaming/progress';
export { StreamCoordinator } from './streaming/coordinator';
export { SSEEventHandler } from './streaming/events';

export type {
  StreamingEventHandler,
  StreamingConfig,
} from './types';

// ===== AGENT EXPORTS =====

export { BaseAgent } from './agents/base';
export { ResearchAgent } from './agents/research';
export { AnalysisAgent } from './agents/analysis';
export { FactCheckAgent } from './agents/fact-check';
export { CompressionAgent } from './agents/compression';
export { ReportAgent } from './agents/report';

// ===== TOOL EXPORTS =====

export { braveSearchTool } from './tools/brave-search';
export { perplexityTool } from './tools/perplexity';
export { firecrawlTool } from './tools/firecrawl';
export { documentAnalyzerTool } from './tools/document-analyzer';
export { factVerifierTool } from './tools/fact-verifier';
export { citationGeneratorTool } from './tools/citation-generator';

export type {
  ResearchTool,
  ToolRateLimit,
} from './types';

// ===== UTILITY EXPORTS =====

export { ModelSelector } from './utils/model-selection';
export { RateLimiter } from './utils/rate-limiter';
export { PerformanceMonitor } from './utils/performance';

// ===== MEMORY/CACHING EXPORTS =====

export { SessionManager } from './memory/session';
export { KnowledgeBase } from './memory/knowledge';

export type {
  ResearchSession,
  ResearchCache,
} from './types';

// ===== VALIDATION EXPORTS =====

export { InputValidator } from './validation/input';
export { OutputValidator } from './validation/output';

// ===== CONVENIENCE FUNCTIONS =====

/**
 * Create a new deep research session with default configuration
 * 
 * @param query - Research query string
 * @param options - Optional research configuration
 * @param context - Optional research context
 * @returns Promise resolving to research result
 * 
 * @example
 * ```typescript
 * import { startResearch } from '@/lib/ai/deep-research';
 * 
 * const result = await startResearch(
 *   "What are the latest developments in AI safety research?",
 *   {
 *     researchQuality: 'comprehensive',
 *     analysisDepth: 'deep',
 *     maxUrls: 50,
 *     timeLimit: 300
 *   }
 * );
 * 
 * console.log(result.analysis);
 * console.log(result.sources);
 * ```
 */
export async function startResearch(
  query: string,
  options?: DeepResearchOptions,
  context?: ResearchContext
): Promise<DeepResearchResult> {
  const { DeepResearchOrchestrator } = await import('./orchestrator');
  const orchestrator = new DeepResearchOrchestrator();
  
  return orchestrator.research({
    query,
    options,
    context,
  });
}

/**
 * Create a streaming research session with real-time progress updates
 * 
 * @param query - Research query string
 * @param options - Optional research configuration
 * @param eventHandler - Streaming event handler for progress updates
 * @param context - Optional research context
 * @returns Promise resolving to research result
 * 
 * @example
 * ```typescript
 * import { startStreamingResearch } from '@/lib/ai/deep-research';
 * 
 * const result = await startStreamingResearch(
 *   "Analyze the current state of renewable energy adoption globally",
 *   {
 *     researchQuality: 'exhaustive',
 *     enableFactChecking: true
 *   },
 *   {
 *     onProgress: (update) => console.log(`Progress: ${update.progress}%`),
 *     onAgent: (agent, task) => console.log(`Agent ${agent.name} working on: ${task.type}`),
 *     onResult: (partial) => console.log('Intermediate result:', partial.analysis),
 *     onError: (error) => console.error('Research error:', error),
 *     onComplete: (final) => console.log('Research complete!')
 *   }
 * );
 * ```
 */
export async function startStreamingResearch(
  query: string,
  options?: DeepResearchOptions,
  eventHandler?: StreamingEventHandler,
  context?: ResearchContext
): Promise<DeepResearchResult> {
  const { DeepResearchOrchestrator } = await import('./orchestrator');
  const orchestrator = new DeepResearchOrchestrator();
  
  return orchestrator.research({
    query,
    options: options || {},
    context,
  }, eventHandler);
}

/**
 * Quick research function for simple queries with default settings
 * 
 * @param query - Research query string
 * @returns Promise resolving to research result
 * 
 * @example
 * ```typescript
 * import { quickResearch } from '@/lib/ai/deep-research';
 * 
 * const result = await quickResearch("What is quantum computing?");
 * console.log(result.summary);
 * ```
 */
export async function quickResearch(query: string): Promise<DeepResearchResult> {
  return startResearch(query, {
    researchQuality: 'basic',
    analysisDepth: 'surface',
    maxUrls: 10,
    timeLimit: 60,
    maxConcurrentAgents: 3,
  });
}

/**
 * Comprehensive research function for complex queries requiring deep analysis
 * 
 * @param query - Research query string
 * @param options - Optional additional configuration
 * @returns Promise resolving to research result
 * 
 * @example
 * ```typescript
 * import { comprehensiveResearch } from '@/lib/ai/deep-research';
 * 
 * const result = await comprehensiveResearch(
 *   "Analyze the economic impact of climate change on global supply chains"
 * );
 * console.log(result.report);
 * console.log(result.citations);
 * ```
 */
export async function comprehensiveResearch(
  query: string,
  options?: Partial<DeepResearchOptions>
): Promise<DeepResearchResult> {
  return startResearch(query, {
    researchQuality: 'exhaustive',
    analysisDepth: 'deep',
    factCheckingLevel: 'rigorous',
    maxUrls: 100,
    timeLimit: 600,
    maxConcurrentAgents: 8,
    enableParallelization: true,
    includeReferences: true,
    includeSummary: true,
    ...options,
  });
}

/**
 * Validate a research request before processing
 * 
 * @param request - Deep research request to validate
 * @returns Validation result with success status and any errors
 * 
 * @example
 * ```typescript
 * import { validateResearchRequest } from '@/lib/ai/deep-research';
 * 
 * const validation = validateResearchRequest({
 *   query: "What is AI?",
 *   options: { maxUrls: 5 }
 * });
 * 
 * if (!validation.valid) {
 *   console.error('Validation errors:', validation.errors);
 * }
 * ```
 */
export async function validateResearchRequest(
  request: DeepResearchRequest
): Promise<{ valid: boolean; errors: string[] }> {
  const { InputValidator } = await import('./validation/input');
  const validator = new InputValidator();
  return validator.validate(request);
}

/**
 * Get research session status and progress information
 * 
 * @param sessionId - Research session identifier
 * @returns Session status and progress information
 * 
 * @example
 * ```typescript
 * import { getResearchStatus } from '@/lib/ai/deep-research';
 * 
 * const status = await getResearchStatus('session-123');
 * console.log(`Status: ${status.status}, Progress: ${status.progress}%`);
 * ```
 */
export async function getResearchStatus(sessionId: string): Promise<ResearchSession | null> {
  const { SessionManager } = await import('./memory/session');
  const sessionManager = new SessionManager();
  return sessionManager.getSession(sessionId);
}

/**
 * Cancel an active research session
 * 
 * @param sessionId - Research session identifier
 * @returns Success status
 * 
 * @example
 * ```typescript
 * import { cancelResearch } from '@/lib/ai/deep-research';
 * 
 * const cancelled = await cancelResearch('session-123');
 * if (cancelled) {
 *   console.log('Research session cancelled successfully');
 * }
 * ```
 */
export async function cancelResearch(sessionId: string): Promise<boolean> {
  const { SessionManager } = await import('./memory/session');
  const sessionManager = new SessionManager();
  return sessionManager.cancelSession(sessionId);
}

/**
 * Get cached research results for a query
 * 
 * @param query - Research query string
 * @returns Cached research result if available
 * 
 * @example
 * ```typescript
 * import { getCachedResearch } from '@/lib/ai/deep-research';
 * 
 * const cached = await getCachedResearch("What is machine learning?");
 * if (cached) {
 *   console.log('Using cached result:', cached.analysis);
 * }
 * ```
 */
export async function getCachedResearch(query: string): Promise<DeepResearchResult | null> {
  const { KnowledgeBase } = await import('./memory/knowledge');
  const knowledgeBase = new KnowledgeBase();
  return knowledgeBase.getCachedResult(query);
}

/**
 * Clear cached research results (useful for testing or memory management)
 * 
 * @param olderThanMs - Optional: only clear cache entries older than this many milliseconds
 * @returns Number of cache entries cleared
 * 
 * @example
 * ```typescript
 * import { clearResearchCache } from '@/lib/ai/deep-research';
 * 
 * // Clear all cache
 * const cleared = await clearResearchCache();
 * 
 * // Clear cache older than 1 hour
 * const oldCleared = await clearResearchCache(3600000);
 * ```
 */
export async function clearResearchCache(olderThanMs?: number): Promise<number> {
  const { KnowledgeBase } = await import('./memory/knowledge');
  const knowledgeBase = new KnowledgeBase();
  return knowledgeBase.clearCache(olderThanMs);
}

/**
 * Get system health and performance metrics
 * 
 * @returns System health information
 * 
 * @example
 * ```typescript
 * import { getSystemHealth } from '@/lib/ai/deep-research';
 * 
 * const health = await getSystemHealth();
 * console.log(`Active sessions: ${health.activeSessions}`);
 * console.log(`System load: ${health.systemLoad}`);
 * ```
 */
export async function getSystemHealth(): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy';
  activeSessions: number;
  activeAgents: number;
  systemLoad: number;
  memoryUsage: number;
  uptime: number;
  lastError?: string;
}> {
  const { PerformanceMonitor } = await import('./utils/performance');
  const monitor = new PerformanceMonitor();
  return monitor.getSystemHealth();
}

// ===== VERSION INFO =====

/**
 * Deep Research Module version information
 */
export const DEEP_RESEARCH_VERSION = {
  version: '1.0.0',
  buildDate: new Date().toISOString(),
  features: [
    'Multi-agent research coordination',
    'AI SDK v5 streaming integration', 
    'Real-time progress tracking',
    'Fact-checking and verification',
    'Source reliability assessment',
    'Citation generation',
    'Comprehensive caching',
    'Rate limiting and monitoring',
  ],
  compatibility: {
    aiSdkVersion: '^5.0.0',
    nodeVersion: '>=18.0.0',
    nextVersion: '>=15.0.0',
  },
} as const;

// ===== DEFAULT EXPORT =====

/**
 * Default export provides the main orchestrator class
 */
export { DeepResearchOrchestrator as default } from './orchestrator';