# 🎨 HTML Canvas Workspace - Quick Start Plan

## Goal: MVP in 1 Week - HTML/JavaScript Canvas Only

### Phase 1: Core Implementation (Days 1-3)

#### Day 1: Setup & Basic Components
```bash
# Install minimal packages
npm install @monaco-editor/react

# No Sandpack needed for pure HTML/JS
```

#### Day 2: Database & API
```sql
-- Single table
CREATE TABLE artifacts (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36),
  content LONGTEXT,
  language VARCHAR(20) DEFAULT 'html',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

```typescript
// /app/api/artifacts/route.ts
export async function POST(req) {
  const { content } = await req.json()
  const artifact = await db.artifacts.create({
    data: { id: nanoid(), userId: session.user.id, content }
  })
  return Response.json({ artifact })
}
```

#### Day 3: Workspace Component
```typescript
// /components/HTMLCanvas.tsx
const HTMLCanvas = ({ code, onChange }) => {
  return (
    <div className="flex h-full">
      <div className="w-1/2">
        <Editor
          language="html"
          value={code}
          onChange={onChange}
          theme="vs-dark"
        />
      </div>
      <div className="w-1/2">
        <iframe
          srcDoc={code}
          sandbox="allow-scripts"
          className="w-full h-full"
        />
      </div>
    </div>
  )
}
```

### Phase 2: Integration (Days 4-5)

#### Chat Integration
```typescript
// Add to chat-interface.tsx
const [showCanvas, setShowCanvas] = useState(false)

// Detect HTML in messages, add "Open Canvas" button
if (message.includes('<html>') || message.includes('<!DOCTYPE')) {
  return <button onClick={() => openCanvas(extractHTML(message))}>
    🎨 Open in Canvas
  </button>
}
```

#### AI Tool
```typescript
// Add to tool registry
const htmlCanvasTool = tool({
  name: 'create_html_canvas',
  description: 'Create HTML/CSS/JS canvas',
  parameters: z.object({ html: z.string() }),
  execute: async ({ html }) => {
    const artifact = await createArtifact({ content: html })
    return { artifactId: artifact.id }
  }
})
```

### Phase 3: Polish (Days 6-7)

- Save/load functionality
- HTML templates (landing page, dashboard, etc.)
- Error handling for iframe
- Mobile responsive design

### MVP Features
✅ HTML/CSS/JavaScript editing
✅ Live preview
✅ Save to database  
✅ AI integration
✅ Security via iframe sandbox

### Future Enhancements
- React support (add Sandpack)
- Python support (add Pyodide)
- Collaboration (add Yjs)
- File management
- Export capabilities

**Total effort: 7 days, zero infrastructure, maximum impact!**

---

## Complete Implementation Guide

### File Structure
```
src/
├── components/
│   ├── canvas/
│   │   ├── HTMLCanvas.tsx
│   │   └── CanvasToolbar.tsx
│   └── chat/
│       └── chat-interface.tsx (modified)
├── app/api/
│   └── artifacts/
│       └── route.ts
└── lib/
    └── canvas-utils.ts
```

### Complete Code Examples

#### 1. HTMLCanvas Component
```typescript
'use client'
import { useState, useRef } from 'react'
import Editor from '@monaco-editor/react'

interface HTMLCanvasProps {
  initialCode?: string
  onSave?: (code: string) => void
}

export const HTMLCanvas = ({ initialCode = '', onSave }: HTMLCanvasProps) => {
  const [code, setCode] = useState(initialCode)
  const [isRunning, setIsRunning] = useState(false)
  const iframeRef = useRef<HTMLIFrameElement>(null)

  const runCode = () => {
    if (iframeRef.current) {
      const iframe = iframeRef.current
      iframe.srcdoc = code
      setIsRunning(true)
    }
  }

  const handleSave = async () => {
    if (onSave) {
      await onSave(code)
    }
  }

  return (
    <div className="flex flex-col h-full">
      {/* Toolbar */}
      <div className="flex items-center gap-2 p-3 border-b bg-gray-50">
        <button 
          onClick={runCode}
          className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          ▶ Run
        </button>
        <button 
          onClick={handleSave}
          className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600"
        >
          💾 Save
        </button>
        <span className="text-sm text-gray-600">
          HTML/CSS/JavaScript Canvas
        </span>
      </div>

      {/* Editor and Preview */}
      <div className="flex flex-1">
        <div className="w-1/2 border-r">
          <Editor
            height="100%"
            defaultLanguage="html"
            value={code}
            onChange={(value) => setCode(value || '')}
            theme="vs-dark"
            options={{
              minimap: { enabled: false },
              fontSize: 14,
              wordWrap: 'on',
              automaticLayout: true
            }}
          />
        </div>
        <div className="w-1/2 bg-white">
          <iframe
            ref={iframeRef}
            srcDoc={code}
            sandbox="allow-scripts allow-forms allow-modals"
            className="w-full h-full border-0"
            title="HTML Preview"
          />
        </div>
      </div>
    </div>
  )
}
```

#### 2. Chat Interface Integration
```typescript
// Add to existing chat-interface.tsx
import { HTMLCanvas } from '@/components/canvas/HTMLCanvas'

// Add state
const [showCanvas, setShowCanvas] = useState(false)
const [canvasCode, setCanvasCode] = useState('')

// Add canvas detection
const detectHTMLContent = (content: string) => {
  const htmlPatterns = [
    /<html/i, /<!doctype/i, /<head>/i, /<body>/i,
    /<div.*>/i, /<script.*>/i, /<style.*>/i
  ]
  return htmlPatterns.some(pattern => pattern.test(content))
}

// Add to message rendering
const renderMessage = (message) => {
  const hasHTML = detectHTMLContent(message.content)
  
  return (
    <div>
      <ReactMarkdown>{message.content}</ReactMarkdown>
      {hasHTML && (
        <button
          onClick={() => {
            const extracted = extractHTMLFromMessage(message.content)
            setCanvasCode(extracted)
            setShowCanvas(true)
          }}
          className="mt-2 text-sm bg-blue-100 text-blue-700 px-3 py-1 rounded-full hover:bg-blue-200"
        >
          🎨 Open in Canvas
        </button>
      )}
    </div>
  )
}

// Modified layout
return (
  <div className="flex h-full">
    <div className={cn("flex-1", showCanvas && "w-1/2")}>
      {/* Existing chat UI */}
    </div>
    
    {showCanvas && (
      <div className="w-1/2 border-l">
        <div className="flex justify-between items-center p-3 border-b">
          <h3 className="font-semibold">HTML Canvas</h3>
          <button
            onClick={() => setShowCanvas(false)}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>
        <HTMLCanvas
          initialCode={canvasCode}
          onSave={async (code) => {
            await saveArtifact({ content: code, type: 'html' })
          }}
        />
      </div>
    )}
  </div>
)
```

#### 3. API Implementation
```typescript
// /app/api/artifacts/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { nanoid } from 'nanoid'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { content, type = 'html', title } = await request.json()

    const artifact = await prisma.artifacts.create({
      data: {
        id: nanoid(),
        userId: session.user.id,
        content,
        language: type,
        title: title || `Untitled ${type}`,
        createdAt: new Date()
      }
    })

    return NextResponse.json({ artifact })
  } catch (error) {
    return NextResponse.json({ error: 'Failed to create artifact' }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  const session = await getServerSession()
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const artifacts = await prisma.artifacts.findMany({
    where: { userId: session.user.id },
    orderBy: { createdAt: 'desc' },
    take: 50
  })

  return NextResponse.json({ artifacts })
}
```

#### 4. AI Tool Integration
```typescript
// Add to existing tool registry
import { tool } from 'ai'
import { z } from 'zod'

export const createHTMLCanvasTool = tool({
  name: 'create_html_canvas',
  description: 'Create an interactive HTML/CSS/JavaScript canvas for web development',
  parameters: z.object({
    html: z.string().describe('Complete HTML content with CSS and JavaScript'),
    title: z.string().optional().describe('Title for the canvas')
  }),
  execute: async ({ html, title }) => {
    try {
      const response = await fetch('/api/artifacts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: html,
          type: 'html',
          title: title || 'HTML Canvas'
        })
      })

      const { artifact } = await response.json()
      
      return {
        success: true,
        artifactId: artifact.id,
        message: `Created HTML canvas: ${artifact.title}. Click "Open in Canvas" to edit and preview.`
      }
    } catch (error) {
      return {
        success: false,
        error: 'Failed to create HTML canvas'
      }
    }
  }
})
```

#### 5. Database Migration
```sql
-- Create artifacts table
CREATE TABLE artifacts (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  title VARCHAR(255) NOT NULL,
  content LONGTEXT NOT NULL,
  language VARCHAR(50) DEFAULT 'html',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_user_id (user_id),
  INDEX idx_created_at (created_at),
  INDEX idx_language (language)
);
```

#### 6. Utility Functions
```typescript
// /lib/canvas-utils.ts
export const extractHTMLFromMessage = (content: string): string => {
  // Extract code blocks
  const codeBlockRegex = /```html\n([\s\S]*?)\n```/g
  const match = codeBlockRegex.exec(content)
  if (match) return match[1]

  // Extract inline HTML
  const htmlRegex = /<html[\s\S]*<\/html>/i
  const htmlMatch = htmlRegex.exec(content)
  if (htmlMatch) return htmlMatch[0]

  // Default template
  return `<!DOCTYPE html>
<html>
<head>
  <title>New Canvas</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 40px; }
  </style>
</head>
<body>
  <h1>Hello Canvas!</h1>
  <script>
    console.log('Canvas ready!');
  </script>
</body>
</html>`
}

export const validateHTML = (html: string): boolean => {
  try {
    const parser = new DOMParser()
    const doc = parser.parseFromString(html, 'text/html')
    return !doc.querySelector('parsererror')
  } catch {
    return false
  }
}
```

### Installation Steps
```bash
# 1. Install dependencies
npm install @monaco-editor/react

# 2. Create database table
npx prisma migrate dev --name add_artifacts_table

# 3. Add components to your project
# Copy HTMLCanvas.tsx to src/components/canvas/
# Update chat-interface.tsx with integration code
# Add API routes

# 4. Test
npm run dev
```

### Security Notes
- iframe sandbox prevents malicious scripts from accessing parent
- Content validation on server side
- User authentication required for all operations
- No external network access from iframe

This complete implementation gives you a working HTML Canvas in your chat application with zero infrastructure requirements!