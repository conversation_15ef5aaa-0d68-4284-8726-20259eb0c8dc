import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { nanoid } from 'nanoid'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { content, type = 'html', title } = await request.json()

    if (!content) {
      return NextResponse.json({ error: 'Content is required' }, { status: 400 })
    }

    const artifact = await prisma.artifacts.create({
      data: {
        id: nanoid(),
        userId: session.user.id,
        content,
        language: type,
        title: title || `Untitled ${type}`,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    })

    return NextResponse.json({ artifact })
  } catch (error) {
    console.error('Failed to create artifact:', error)
    return NextResponse.json({ error: 'Failed to create artifact' }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const artifacts = await prisma.artifacts.findMany({
      where: { userId: session.user.id },
      orderBy: { createdAt: 'desc' },
      take: 50
    })

    return NextResponse.json({ artifacts })
  } catch (error) {
    console.error('Failed to fetch artifacts:', error)
    return NextResponse.json({ error: 'Failed to fetch artifacts' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id, content, title } = await request.json()

    if (!id || !content) {
      return NextResponse.json({ error: 'ID and content are required' }, { status: 400 })
    }

    const artifact = await prisma.artifacts.update({
      where: { 
        id,
        userId: session.user.id // Ensure user owns this artifact
      },
      data: {
        content,
        title,
        updatedAt: new Date()
      }
    })

    return NextResponse.json({ artifact })
  } catch (error) {
    console.error('Failed to update artifact:', error)
    return NextResponse.json({ error: 'Failed to update artifact' }, { status: 500 })
  }
}