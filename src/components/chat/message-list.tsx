'use client';

import { Message, MessageRole } from '@/types';
import { MessageItem } from './message-item';
import { motion, AnimatePresence } from 'framer-motion';
import { LayoutStabilizer } from './LayoutStabilizer';
import { WebSearchIndicator } from './web-search-indicator';
import { SmoothStreamingMessage } from './smooth-streaming-message';
import { Loader2 } from 'lucide-react';

interface MessageListProps {
  messages: Message[];
  streamingMessage?: Message | null;
  onRetryMessage?: (messageId: string) => void;
  webSearchActive?: boolean;
  webSearchQuery?: string;
  webSearchQueries?: string[]; // Router-generated search queries
  webSearchResults?: number;
  isLoading: boolean;
  isProcessingAttachments: boolean;
  currentAssistantMessage: string;
  toolCalls?: any[];
  activeToolCalls?: any[];
  onOpenCanvas?: (code: string) => void;
}

export function MessageList({ 
  messages, 
  streamingMessage, 
  onRetryMessage,
  webSearchActive = false,
  webSearchQuery,
  webSearchQueries,
  webSearchResults,
  isLoading,
  isProcessingAttachments,
  currentAssistantMessage,
  toolCalls = [],
  activeToolCalls = [],
  onOpenCanvas
}: MessageListProps) {
  // Only show regular messages, streaming is handled by SmoothStreamingMessage
  const allMessages = messages;

  return (
    <div className="py-4 stable-layout">
      {allMessages.map((message, index) => (
        <motion.div
          key={message.id}
          initial={{ opacity: 0, y: 3 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ 
            duration: 0.15,
            delay: Math.min(index * 0.02, 0.1), // Reduced delay for smoother experience
            ease: 'easeOut'
          }}
        >
          <LayoutStabilizer
            minHeight={message.role === 'assistant' ? 80 : 40}
            smoothScroll={true}
          >
            <MessageItem 
              message={message}
              isStreaming={message.id === streamingMessage?.id}
              onRetry={onRetryMessage && message.role === 'assistant' ? () => onRetryMessage(message.id) : undefined}
              webSearchActive={webSearchActive && message.id === streamingMessage?.id}
              webSearchQuery={webSearchQuery}
              webSearchQueries={webSearchQueries}
              webSearchResults={webSearchResults}
              onOpenCanvas={onOpenCanvas}
            />
          </LayoutStabilizer>
        </motion.div>
      ))}
      {/* Smooth streaming component that handles loading -> streaming transition */}
      {/* Show response container immediately when loading starts or when streaming */}
      {(isLoading || streamingMessage) && (
        <SmoothStreamingMessage
          isLoading={isLoading}
          isProcessingAttachments={isProcessingAttachments}
          currentAssistantMessage={currentAssistantMessage}
          streamingMessage={streamingMessage || null}
          webSearchActive={webSearchActive}
          webSearchQuery={webSearchQuery}
          webSearchQueries={webSearchQueries}
          webSearchResults={webSearchResults}
          toolCalls={toolCalls}
          activeToolCalls={activeToolCalls}
        />
      )}
    </div>
  );
}