/**
 * Deep Research Orchestrator - Core Implementation
 * 
 * @description
 * Main orchestrator class that coordinates the entire deep research process.
 * Integrates AI SDK v5 streaming, LangChain agent management, and real-time
 * progress tracking with SSE-compatible event streams.
 * 
 * @module lib/ai/deep-research/orchestrator
 */

import { streamText, generateText, tool } from 'ai';
import {
  ResearchPhase,
  ResearchSessionStatus,
  ResearchError,
  AgentStatus
} from './types';
import type { 
  DeepResearchRequest,
  DeepResearchOptions,
  DeepResearchResult,
  ResearchContext,
  ResearchProgressUpdate,
  ResearchSession,
  ResearchProgressType,
  StreamingEventHandler,
  DeepResearchConfig
} from './types';
import { deepResearchConfig } from './config';
import { ResearchSupervisor } from './supervisor';
import { StreamCoordinator } from './streaming/coordinator';
import { ProgressTracker } from './streaming/progress';
import { SessionManager } from './memory/session';
import { KnowledgeBase } from './memory/knowledge';
import { InputValidator } from './validation/input';
import { ModelSelector } from './utils/model-selection';
import { RateLimiter } from './utils/rate-limiter';
import { PerformanceMonitor } from './utils/performance';
import { routerLogger } from '@/lib/logger';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';

/**
 * Deep Research Orchestrator
 * 
 * Manages the complete deep research workflow including agent coordination,
 * streaming progress updates, and result generation.
 */
export class DeepResearchOrchestrator {
  private config = deepResearchConfig.getConfig();
  private supervisor: ResearchSupervisor;
  private streamCoordinator: StreamCoordinator;
  private progressTracker: ProgressTracker;
  private sessionManager: SessionManager;
  private knowledgeBase: KnowledgeBase;
  private inputValidator: InputValidator;
  private modelSelector: ModelSelector;
  private rateLimiter: RateLimiter;
  private performanceMonitor: PerformanceMonitor;
  private logger = routerLogger;

  constructor() {
    // Initialize core components
    this.supervisor = new ResearchSupervisor();
    this.streamCoordinator = new StreamCoordinator();
    this.progressTracker = new ProgressTracker();
    this.sessionManager = new SessionManager();
    this.knowledgeBase = new KnowledgeBase();
    this.inputValidator = new InputValidator();
    this.modelSelector = new ModelSelector();
    this.rateLimiter = new RateLimiter();
    this.performanceMonitor = new PerformanceMonitor();
  }

  /**
   * Execute deep research with optional streaming
   */
  async research(
    request: DeepResearchRequest,
    eventHandler?: StreamingEventHandler
  ): Promise<DeepResearchResult> {
    const sessionId = uuidv4();
    const timer = this.performanceMonitor.startTimer('deep-research');
    
    try {
      // Validate request
      const validation = await this.inputValidator.validate(request);
      if (!validation.valid) {
        throw new ResearchError(`Invalid request: ${validation.errors.join(', ')}`);
      }

      // Check rate limits
      const rateLimitOk = await this.rateLimiter.checkLimit(
        request.userId || 'anonymous',
        request.context?.user?.plan || 'FREE'
      );
      if (!rateLimitOk) {
        throw new ResearchError('Rate limit exceeded', ResearchPhase.PLANNING);
      }

      // Check cache
      const cached = await this.knowledgeBase.getCachedResult(request.query);
      if (cached && this.isCacheValid(cached, request.options)) {
        this.logger.info('Using cached research result', { query: request.query });
        return cached;
      }

      // Create session
      const session = await this.sessionManager.createSession({
        id: sessionId,
        userId: request.userId,
        query: request.query,
        options: request.options || {},
        status: ResearchSessionStatus.INITIALIZING,
        phase: ResearchPhase.PLANNING,
        progress: 0,
        agents: [],
        tasks: [],
        intermediateResults: [],
        startTime: new Date(),
        errors: [],
        retryCount: 0,
        metadata: {}
      });

      // Execute research with streaming
      if (eventHandler) {
        return await this.streamingResearch(session, request, eventHandler);
      } else {
        return await this.standardResearch(session, request);
      }

    } catch (error) {
      this.logger.error('Deep research failed', error);
      await this.sessionManager.updateSession(sessionId, {
        status: ResearchSessionStatus.FAILED,
        errors: [error as ResearchError]
      });
      throw error;
    } finally {
      timer();
    }
  }

  /**
   * Execute research with streaming progress updates
   */
  async streamingResearch(
    session: ResearchSession,
    request: DeepResearchRequest,
    eventHandler?: StreamingEventHandler
  ): Promise<DeepResearchResult> {
    try {
      // Initialize progress tracking with event handler
      if (eventHandler) {
        this.progressTracker.initialize(session.id, undefined, eventHandler);
      }
          
          // Phase 1: Planning
          await this.progressTracker.updatePhase(session.id, ResearchPhase.PLANNING, 'Analyzing query and planning research strategy');
          const researchPlan = await this.supervisor.planResearch(request.query, request.options);
          await this.progressTracker.updateProgress(session.id, 20, 'Research plan created');

          // Phase 2: Agent Coordination
          await this.progressTracker.updatePhase(session.id, ResearchPhase.SEARCH, 'Deploying research agents');
          const compatibleConfig: DeepResearchConfig = {
            maxConcurrentSessions: this.config.systemLimits.maxConcurrentSessions,
            maxConcurrentAgents: this.config.systemLimits.maxConcurrentAgents,
            defaultTimeout: this.config.systemLimits.defaultTimeoutMs,
            maxTimeout: this.config.systemLimits.maxTimeoutMs,
            defaultQuality: 'comprehensive',
            defaultDepth: 'moderate',
            models: this.config.models,
            tools: [],
            enableCaching: true,
            cacheTimeout: 3600,
            enableParallelization: true,
            enableMetrics: true,
            enableDetailedLogging: false,
            rateLimits: {
              perUser: 10,
              perSession: 50,
              global: 100
            }
          };
          const agents = await this.supervisor.spawnAgents(researchPlan, compatibleConfig);
          await this.progressTracker.updateProgress(session.id, 30, `${agents.length} research agents deployed`);

          // Phase 3: Parallel Research Execution
          const findings = await this.executeParallelResearch(session, agents, eventHandler);
          await this.progressTracker.updateProgress(session.id, 60, 'Research data collected');

          // Phase 4: Analysis
          await this.progressTracker.updatePhase(session.id, ResearchPhase.ANALYSIS, 'Analyzing research findings');
          const analysis = await this.supervisor.analyzeFindings(findings);
          await this.progressTracker.updateProgress(session.id, 80, 'Analysis complete');

          // Phase 5: Report Generation
          await this.progressTracker.updatePhase(session.id, ResearchPhase.REPORT, 'Generating comprehensive report');
          const report = await this.generateFinalReport(request, analysis, findings);
          await this.progressTracker.updateProgress(session.id, 100, 'Research complete');

          // Send final result through event handler if available
          if (eventHandler) {
            await eventHandler.onComplete?.(report);
          }

          // Cache result
          await this.knowledgeBase.cacheResult(request.query, report);

          // Update session
          await this.sessionManager.updateSession(session.id, {
            status: ResearchSessionStatus.COMPLETED,
            finalResult: report,
            endTime: new Date()
          });

          return report;

        } catch (error) {
          if (eventHandler) {
            await eventHandler.onError?.(error as Error);
          }
          await this.handleStreamingError(session.id, error as Error);
          throw error;
        }
  }

  /**
   * Execute standard research without streaming
   */
  async standardResearch(
    session: ResearchSession,
    request: DeepResearchRequest
  ): Promise<DeepResearchResult> {
    // Similar to streaming but without progress updates
    const researchPlan = await this.supervisor.planResearch(request.query, request.options);
    const compatibleConfig: DeepResearchConfig = {
      maxConcurrentSessions: this.config.systemLimits.maxConcurrentSessions,
      maxConcurrentAgents: this.config.systemLimits.maxConcurrentAgents,
      defaultTimeout: this.config.systemLimits.defaultTimeoutMs,
      maxTimeout: this.config.systemLimits.maxTimeoutMs,
      defaultQuality: 'comprehensive',
      defaultDepth: 'moderate',
      models: this.config.models,
      tools: [],
      enableCaching: true,
      cacheTimeout: 3600,
      enableParallelization: true,
      enableMetrics: true,
      enableDetailedLogging: false,
      rateLimits: {
        perUser: 10,
        perSession: 50,
        global: 100
      }
    };
    const agents = await this.supervisor.spawnAgents(researchPlan, compatibleConfig);
    const findings = await this.executeStandardResearch(session, agents);
    const analysis = await this.supervisor.analyzeFindings(findings);
    const report = await this.generateFinalReport(request, analysis, findings);
    
    await this.knowledgeBase.cacheResult(request.query, report);
    await this.sessionManager.updateSession(session.id, {
      status: ResearchSessionStatus.COMPLETED,
      finalResult: report,
      endTime: new Date()
    });
    
    return report;
  }

  /**
   * Execute parallel research with streaming updates
   */
  private async executeParallelResearch(
    session: ResearchSession,
    agents: any[],
    eventHandler?: StreamingEventHandler
  ): Promise<any[]> {
    const findings: any[] = [];
    const totalAgents = agents.length;
    let completedAgents = 0;

    // Execute agents in parallel with progress tracking
    await Promise.all(
      agents.map(async (agent, index) => {
        try {
          // Update agent status
          if (eventHandler && eventHandler.onAgent) {
            await eventHandler.onAgent(agent, {
              id: crypto.randomUUID(),
              agentId: agent.id,
              type: agent.type,
              priority: 'normal' as any,
              status: AgentStatus.INITIALIZING,
              query: `Starting ${agent.type} agent`,
              startTime: new Date()
            });
          }

          // Execute agent research
          const agentFindings = await agent.research();
          findings.push({
            agentId: agent.id,
            agentType: agent.type,
            findings: agentFindings
          });

          // Update progress
          completedAgents++;
          const progress = 30 + Math.floor((completedAgents / totalAgents) * 30);
          await this.progressTracker.updateProgress(
            session.id,
            progress,
            `Agent ${agent.id} completed (${completedAgents}/${totalAgents})`
          );

          // Stream agent findings
          if (eventHandler && eventHandler.onAgent) {
            await eventHandler.onAgent(agent, {
              id: crypto.randomUUID(),
              agentId: agent.id,
              type: agent.type,
              priority: 'normal' as any,
              status: AgentStatus.COMPLETED,
              query: `${agent.type} agent completed`,
              endTime: new Date(),
              result: {
                success: true,
                data: agentFindings
              }
            });
          }

        } catch (error) {
          this.logger.error(`Agent ${agent.id} failed`, error);
          if (eventHandler && eventHandler.onAgent) {
            await eventHandler.onAgent(agent, {
              id: crypto.randomUUID(),
              agentId: agent.id,
              type: agent.type,
              priority: 'normal' as any,
              status: AgentStatus.FAILED,
              query: `${agent.type} agent failed`,
              endTime: new Date(),
              error: new ResearchError(
                (error as Error).message,
                ResearchPhase.SEARCH,
                agent.id
              )
            });
          }
        }
      })
    );

    return findings;
  }

  /**
   * Execute standard research without streaming
   */
  private async executeStandardResearch(
    session: ResearchSession,
    agents: any[]
  ): Promise<any[]> {
    const findings: any[] = [];
    
    await Promise.all(
      agents.map(async (agent) => {
        try {
          const agentFindings = await agent.research();
          findings.push({
            agentId: agent.id,
            agentType: agent.type,
            findings: agentFindings
          });
        } catch (error) {
          this.logger.error(`Agent ${agent.id} failed`, error);
        }
      })
    );

    return findings;
  }

  /**
   * Generate final research report
   */
  private async generateFinalReport(
    request: DeepResearchRequest,
    analysis: any,
    findings: any[]
  ): Promise<DeepResearchResult> {
    const reportModel = await this.modelSelector.selectModel(
      'report',
      request.context?.user?.plan
    );

    // Use generateText for more reliable processing in orchestrator context
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 60000); // 1 minute timeout for reports
    
    let reportText: string;
    try {
      const result = await generateText({
        model: reportModel,
        messages: [
          {
            role: 'system',
            content: `You are a comprehensive research report generator. Create a detailed, well-structured report based on the provided research findings and analysis.`
          },
          {
            role: 'user',
            content: `Generate a comprehensive research report for the query: "${request.query}"
            
Analysis: ${JSON.stringify(analysis)}
Raw Findings: ${JSON.stringify(findings)}

Create a structured report with:
1. Executive Summary
2. Key Findings
3. Detailed Analysis
4. Sources and Citations
5. Recommendations
6. Conclusion`
          }
        ],
        maxOutputTokens: 8000,
        temperature: 0.3,
        abortSignal: controller.signal
      });
      
      clearTimeout(timeoutId);
      reportText = result.text;
    } catch (error) {
      clearTimeout(timeoutId);
      this.logger.error('Report generation failed', { error });
      reportText = `# Research Report: ${request.query}\n\nReport generation encountered an error. Please try again.`;
    }

    return {
      id: uuidv4(),
      query: request.query,
      success: true,
      analysis: analysis.summary || reportText,
      summary: analysis.executiveSummary || this.extractSummary(reportText),
      report: reportText,
      sources: this.extractSources(findings),
      findings: this.processFindings(findings),
      citations: this.generateCitations(findings),
      confidence: this.calculateConfidence(findings),
      reliability: this.calculateReliability(findings),
      completeness: this.assessCompleteness(findings),
      executionTime: Date.now() - (typeof request.sessionId === 'number' ? request.sessionId : Date.now()),
      tokensUsed: this.calculateTokenUsage(findings),
      agentsUsed: this.getAgentUsageInfo(findings),
      sessionId: request.sessionId || uuidv4(),
      userId: request.userId,
      timestamp: new Date(),
      errors: [],
      warnings: [],
      metadata: {
        researchQuality: request.options?.researchQuality || 'comprehensive',
        analysisDepth: request.options?.analysisDepth || 'moderate'
      }
    };
  }

  /**
   * Handle streaming errors gracefully
   */
  private async handleStreamingError(
    sessionId: string,
    error: Error
  ): Promise<void> {
    this.logger.error('Streaming research error', error);

    await this.sessionManager.updateSession(sessionId, {
      status: ResearchSessionStatus.FAILED,
      errors: [error as ResearchError]
    });
  }

  // Helper methods
  private isCacheValid(cached: DeepResearchResult, options?: DeepResearchOptions): boolean {
    const cacheAge = Date.now() - cached.timestamp.getTime();
    const maxAge = options?.enableCaching ? this.config.quality.optimization.cacheTimeoutMs : 0;
    return cacheAge < maxAge;
  }

  private isRetryableError(error: Error): boolean {
    return error.message.includes('timeout') || error.message.includes('rate limit');
  }

  private extractSummary(report: string): string {
    // Extract executive summary from report
    const summaryMatch = report.match(/Executive Summary[:\n]+(.*?)(?=\n\n|\n#|$)/s);
    return summaryMatch ? summaryMatch[1].trim() : report.substring(0, 500) + '...';
  }

  private extractSources(findings: any[]): any[] {
    // Extract and deduplicate sources from findings
    const sources = new Map();
    findings.forEach(f => {
      if (f.findings?.sources) {
        f.findings.sources.forEach((source: any) => {
          if (!sources.has(source.url)) {
            sources.set(source.url, source);
          }
        });
      }
    });
    return Array.from(sources.values());
  }

  private processFindings(findings: any[]): any[] {
    // Process and structure findings
    return findings.flatMap(f => f.findings?.keyFindings || []);
  }

  private generateCitations(findings: any[]): any[] {
    // Generate citations from findings
    const citations: any[] = [];
    findings.forEach((f, index) => {
      if (f.findings?.sources) {
        f.findings.sources.forEach((source: any) => {
          citations.push({
            id: `cite-${index}-${citations.length}`,
            source,
            citationText: `[${index + 1}] ${source.title}. ${source.url}`,
            format: 'simple',
            inTextReference: `[${index + 1}]`,
            usageCount: 1
          });
        });
      }
    });
    return citations;
  }

  private calculateConfidence(findings: any[]): number {
    // Calculate overall confidence score
    const confidences = findings.map(f => f.findings?.confidence || 0.5);
    return confidences.reduce((a, b) => a + b, 0) / confidences.length;
  }

  private calculateReliability(findings: any[]): number {
    // Calculate source reliability
    const reliabilities = findings.map(f => f.findings?.reliability || 0.5);
    return reliabilities.reduce((a, b) => a + b, 0) / reliabilities.length;
  }

  private assessCompleteness(findings: any[]): number {
    // Assess research completeness
    const hasMultipleSources = findings.length >= 3;
    const hasDiverseTypes = new Set(findings.map(f => f.agentType)).size >= 2;
    const hasDepth = findings.some(f => f.findings?.analysisDepth === 'deep');
    
    let score = 0.5;
    if (hasMultipleSources) score += 0.2;
    if (hasDiverseTypes) score += 0.2;
    if (hasDepth) score += 0.1;
    
    return Math.min(score, 1.0);
  }

  private calculateTokenUsage(findings: any[]): number {
    // Calculate total tokens used
    return findings.reduce((total, f) => total + (f.findings?.tokensUsed || 0), 0);
  }

  private getAgentUsageInfo(findings: any[]): any[] {
    // Get agent usage information
    return findings.map(f => ({
      agentId: f.agentId,
      agentType: f.agentType,
      tasksCompleted: 1,
      executionTime: f.findings?.executionTime || 0,
      tokensUsed: f.findings?.tokensUsed || 0,
      successRate: f.findings ? 1.0 : 0.0
    }));
  }
}