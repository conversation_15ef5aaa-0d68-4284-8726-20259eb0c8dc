'use client'
import { useState, useRef } from 'react'
import Editor from '@monaco-editor/react'

interface HTMLCanvasProps {
  initialCode?: string
  onSave?: (code: string) => void
}

export const HTMLCanvas = ({ initialCode = '', onSave }: HTMLCanvasProps) => {
  const [code, setCode] = useState(initialCode)
  const [isRunning, setIsRunning] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const iframeRef = useRef<HTMLIFrameElement>(null)

  const runCode = () => {
    if (iframeRef.current) {
      const iframe = iframeRef.current
      iframe.srcdoc = code
      setIsRunning(true)
      // Reset running state after a brief delay
      setTimeout(() => setIsRunning(false), 500)
    }
  }

  const handleSave = async () => {
    if (onSave) {
      setIsSaving(true)
      try {
        await onSave(code)
      } catch (error) {
        console.error('Failed to save artifact:', error)
      } finally {
        setIsSaving(false)
      }
    }
  }

  return (
    <div className="flex flex-col h-full">
      {/* Toolbar */}
      <div className="flex items-center gap-2 p-3 border-b bg-gray-50 dark:bg-gray-800 dark:border-gray-700">
        <button 
          onClick={runCode}
          disabled={isRunning}
          className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 text-sm font-medium"
        >
          {isRunning ? '⏳' : '▶'} Run
        </button>
        <button 
          onClick={handleSave}
          disabled={isSaving}
          className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 text-sm font-medium"
        >
          {isSaving ? '⏳' : '💾'} Save
        </button>
        <span className="text-sm text-gray-600 dark:text-gray-400">
          HTML/CSS/JavaScript Canvas
        </span>
      </div>

      {/* Editor and Preview */}
      <div className="flex flex-1">
        <div className="w-1/2 border-r dark:border-gray-700">
          <Editor
            height="100%"
            defaultLanguage="html"
            value={code}
            onChange={(value) => setCode(value || '')}
            theme="vs-dark"
            options={{
              minimap: { enabled: false },
              fontSize: 14,
              wordWrap: 'on',
              automaticLayout: true,
              scrollBeyondLastLine: false,
              renderLineHighlight: 'none',
              selectionHighlight: false,
              occurrencesHighlight: false,
              overviewRulerBorder: false,
              hideCursorInOverviewRuler: true
            }}
          />
        </div>
        <div className="w-1/2 bg-white">
          <iframe
            ref={iframeRef}
            srcDoc={code}
            sandbox="allow-scripts allow-forms allow-modals"
            className="w-full h-full border-0"
            title="HTML Preview"
          />
        </div>
      </div>
    </div>
  )
}

export default HTMLCanvas