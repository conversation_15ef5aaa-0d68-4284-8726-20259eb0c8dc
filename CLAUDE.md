# 🚀 JustSimpleChat Environment Implementation

**Purpose**: Implementation details, environment configurations, and development/deployment procedures for all environments.

IMPORTANT: ALWAYS USE CONTEXT7 TO UNDERSTAND NEW SYNTAX OF AI SDK V5

## 📖 Quick References
- **Project Overview**: `~/deployments/CLAUDE.md`
- **Personal Preferences**: `~/.claude/CLAUDE.md`
- **📚 Comprehensive Documentation**: `/DOCS/README.md` - Detailed guides for all systems
- **🏛️ System Architecture**: [View Architecture Diagram](./system_diagram.svg) - Complete system overview with all layers and integrations
- **🚀 Database-First Architecture**: Complete provider and model management via MySQL database

## 📚 AI Framework Documentation (Local Files)

### 🤖 AI SDK v5 - Complete Documentation
- **File**: `/var/www/dev/llms.txt` (~882KB)
- **Source**: Vercel AI SDK v5 comprehensive documentation
- **Contains**: Complete v5 API reference, examples, migration guides
- **Key Functions**: `streamText()`, `generateText()`, `generateObject()`, `useChat()`
- **Providers**: OpenAI, Anthropic, Google, Mistral, and 20+ others
- **Features**: Tool calling, multi-modal, streaming, structured output
- **Usage**: Search file for specific topics - too large to read entirely

### 🦜 LangChain JavaScript - Core Concepts  
- **File**: `/var/www/dev/langchainjs.txt` (425 lines)
- **Source**: LangChain JavaScript documentation
- **Contains**: Core concepts, components, and patterns
- **Key Topics**: LCEL, RAG, retrievers, embeddings, document loaders
- **Features**: Chat models, tools, structured output, multimodality
- **Best for**: Understanding LangChain architecture and components

### 🕸️ LangGraph - Agent Workflows
- **File**: `/var/www/dev/langgraph.txt` (28K+ lines)
- **Source**: LangGraph.js complete documentation  
- **Contains**: Full agent framework with examples and tutorials
- **Key Concepts**: `StateGraph`, workflows, ReAct agents, memory
- **Features**: Multi-agent systems, conditional logic, state management
- **Best for**: Building complex reasoning agents and workflows

### 🚀 Framework Selection Guide
- **Simple LLM calls**: Use AI SDK v5 (`llms.txt`)
- **RAG and components**: Use LangChain (`langchainjs.txt`) 
- **Complex agents**: Use LangGraph (`langgraph.txt`)
- **Hybrid approach**: Combine all three for comprehensive AI applications

**Search Tips**: All files support grep/search - use specific function names or concepts to find relevant sections quickly.

## 🌐 Environment Quick Access

### Development Environment (`dev.justsimple.chat:3004`)
- **Directory**: `/var/www/dev/simplechat-ai`
- **Branch**: `develop` or feature branches
- **Database**: `justsimplechat` (shared database)
- **PM2 Process**: `simplechat-dev`
- **Health**: `curl https://dev.justsimple.chat/api/health`
- **Logs**: `pm2 logs simplechat-dev --nostream`

### Staging Environment (`staging.justsimple.chat:3005`)
- **Directory**: `/var/www/staging/simplechat-ai`
- **Branch**: `staging`
- **Database**: `justsimplechat` (shared database)
- **PM2 Process**: `simplechat-staging`
- **Health**: `curl https://staging.justsimple.chat/api/health`
- **Logs**: `pm2 logs simplechat-staging --nostream`
- **Deploy**: `cd /var/www/dev/simplechat-ai && ./deploy-staging-v3.sh`
- **Important**: If PM2 restart needed: `pm2 stop simplechat-staging && pm2 start /var/www/staging/simplechat-ai/ecosystem.config.js`

### Production Environment (`justsimple.chat:3006`)
- **Directory**: `/var/www/production/simplechat-ai`
- **Branch**: `main` only
- **Database**: `justsimplechat` (shared database)
- **PM2 Process**: `simplechat-production`
- **Health**: `curl https://justsimple.chat/api/health`
- **Logs**: `pm2 logs simplechat-production --nostream`
- **Deploy**: `cd /var/www/dev/simplechat-ai && ./deploy-production-v3.sh`

## 🔧 Shared Environment Configuration

### 🗄️ Database Configuration
- **MySQL Host**: `127.0.0.1` (Docker container)
- **MySQL User**: `root`
- **MySQL Port**: `3306`
- **MySQL Database**: `justsimplechat` (main database, NOT justsimplechat_dev)
- **MySQL Password**: Available in environment variables:
  - `.env` file: `DATABASE_URL` contains full connection string
  - `~/.bashrc`: `MYSQL_PWD` and `MYSQL_PASSWORD` environment variables
  - Direct access: `echo $MYSQL_PWD` or `echo $MYSQL_PASSWORD`

### 🔍 Quick Database Access
```bash
# Connect to MySQL using environment variables
mysql -h 127.0.0.1 -u root -p$MYSQL_PWD justsimplechat

# Or use the full DATABASE_URL from .env
# mysql://root:$MYSQL_PWD@localhost:3306/justsimplechat

# Prisma Schema Location
# /home/<USER>/deployments/dev/simplechat-ai/prisma/schema.prisma
```

### 📊 Redis Configuration
- **Redis Host**: `localhost` (not `127.0.0.1`)
- **Redis Port**: `6379`
- **Redis URL**: `redis://localhost:6379`

### 🔧 Environment Files
- **Primary**: `.env` (contains all configuration)
- **Local overrides**: `.env.local` (if needed)
- **Environment variables**: Available in `~/.bashrc`

## 🔧 MCP Servers Available

### 🌐 Firecrawl (Web Scraping & Crawling)
- **Purpose**: Advanced web scraping, crawling, and content extraction
- **Key Functions**:
  - `firecrawl_scrape`: Single page content extraction (best for known URLs)
  - `firecrawl_map`: Discover all URLs on a website
  - `firecrawl_crawl`: Extract content from multiple pages (async job)
  - `firecrawl_search`: Web search with optional content scraping
  - `firecrawl_extract`: Structured data extraction using LLM
  - `firecrawl_deep_research`: Complex research across multiple sources
  - `firecrawl_generate_llmstxt`: Generate LLMs.txt files for domains
- **Best for**: Website content extraction, competitive analysis, research
- **Usage**: Always check latest docs for parameters and capabilities
- **API Key**: `fc-9fcee76d691b4452b4fbccc283a8e158`
- **Installation**:
  ```bash
  claude mcp add firecrawl --scope user -e FIRECRAWL_API_KEY=fc-9fcee76d691b4452b4fbccc283a8e158 -- npx -y firecrawl-mcp
  ```

### 📚 Context7 (Documentation & Library Info)
- **Purpose**: Access up-to-date documentation for libraries and frameworks
- **Key Functions**:
  - `resolve-library-id`: Find Context7-compatible library ID from name
  - `get-library-docs`: Fetch current documentation for libraries
- **Best for**: Getting latest docs for React, Next.js, libraries, frameworks
- **Usage**: Always use `resolve-library-id` first unless user provides exact ID
- **Library Format**: IDs like `/mongodb/docs`, `/vercel/next.js`, `/supabase/supabase`
- **Installation** (no API key needed):
  ```bash
  claude mcp add context7 --scope user -- npx -y @upstash/context7-mcp@latest
  ```

### 🔍 Perplexity (AI Search & Research)
- **Purpose**: AI-powered search and research capabilities
- **Key Functions**:
  - `search`: Quick search for simple queries (Sonar Pro model)
  - `reason`: Complex, multi-step reasoning tasks (Sonar Reasoning Pro)
  - `deep_research`: In-depth analysis with detailed reports (Sonar Deep Research)
- **Best for**: Current events, latest information, research questions
- **Usage**: Supports conversation context and can access real-time web data
- **API Key**: `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`
- **Installation**:
  ```bash
  claude mcp add perplexity-ask --scope user -e PERPLEXITY_API_KEY=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 -- npx -y perplexity-mcp
  ```

### 🗄️ MySQL (Database Operations)
- **Purpose**: Direct MySQL database operations
- **Key Functions**:
  - `connect_db`: Connect to MySQL database (host, user, password, database)
  - `query`: Execute SELECT queries with optional parameters
  - `execute`: Execute INSERT, UPDATE, DELETE queries
  - `list_tables`: List all tables in the database
  - `describe_table`: Get table structure and schema
- **Best for**: Database queries, schema inspection, data management
- **Usage**: Connection details from environment variables

### 🧠 Sequential Thinking (Advanced Problem Solving)
- **Purpose**: Dynamic and reflective problem-solving through chain of thought
- **Key Functions**:
  - `sequentialthinking`: Multi-step reasoning with revision capability
- **Best for**: Complex problems, planning, analysis, multi-step solutions
- **Features**: Can revise previous thoughts, branch, and adjust approach

### 🐙 GitHub (Complete GitHub API)
- **Purpose**: Full GitHub repository and project management
- **Key Functions** (23 total):
  - Repository: create, fork, search, get contents, create branch
  - Files: create/update file, push multiple files, get file contents
  - Issues: create, list, update, add comment, search
  - Pull Requests: create, list, get details, review, merge, update branch
  - Code: search code across repos
- **Best for**: Repository management, code collaboration, issue tracking
- **Usage**: Requires GitHub authentication token

### 📋 Task Master AI (Project Management)
- **Purpose**: Comprehensive task and project management
- **Key Functions** (41 total):
  - Project: initialize, models config, rules management, parse PRD
  - Tasks: add, update, remove, set status, expand, get next task
  - Subtasks: add, update, remove, clear, expand all
  - Dependencies: add, remove, validate, fix
  - Tags: list, add, delete, use, rename, copy
  - Analysis: complexity report, research integration
- **Best for**: Project planning, task tracking, dependency management
- **Usage**: Initialize project first, then manage tasks

### 🚨 Important MCP Usage Rules
1. **Always use MCP tools for latest documentation** - Don't rely on outdated knowledge
2. **Check capabilities before assuming** - Each tool has specific use cases
3. **Firecrawl for web content** - Use appropriate function for your needs
4. **Context7 for library docs** - Essential for React 19, Next.js 15, etc.
5. **Perplexity for research** - When you need current, accurate information
6. **MySQL for database operations** - Direct database access when needed
7. **Sequential Thinking for complex problems** - Use for multi-step reasoning
8. **GitHub for repository management** - Complete GitHub API access
9. **Task Master for project tracking** - Comprehensive task management
10. **Verify before implementing** - Always check latest docs before coding

### 🎯 Common MCP Use Cases
- **Library Implementation**: Use Context7 to get latest API docs
- **Web Research**: Use Firecrawl for content extraction or Perplexity for AI search
- **Competitive Analysis**: Use Firecrawl to analyze competitor sites
- **Documentation**: Use Context7 for framework-specific guides
- **Current Events**: Use Perplexity for real-time information
- **Database Operations**: Use MySQL to query and manage database directly
- **Complex Problem Solving**: Use Sequential Thinking for multi-step analysis
- **GitHub Management**: Use GitHub for repository operations and collaboration
- **Project Planning**: Use Task Master AI for comprehensive task management

### 🚀 MCP Server Management

#### Quick Installation (Core MCP Servers)
```bash
# Set timeout first to prevent installation timeouts
export BASH_DEFAULT_TIMEOUT_MS=600000

# Install core MCP servers
claude mcp add perplexity-ask --scope user -e PERPLEXITY_API_KEY=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 -- npx -y perplexity-mcp
claude mcp add firecrawl --scope user -e FIRECRAWL_API_KEY=fc-9fcee76d691b4452b4fbccc283a8e158 -- npx -y firecrawl-mcp
claude mcp add context7 --scope user -- npx -y @upstash/context7-mcp@latest

# Install additional MCP servers (if needed)
# MySQL: claude mcp add mysql --scope user -e MYSQL_HOST=127.0.0.1 -e MYSQL_USER=root -e MYSQL_PASSWORD=$MYSQL_PWD -e MYSQL_DATABASE=justsimplechat -- npx -y @modelcontextprotocol/mysql-mcp
# Sequential Thinking: claude mcp add sequential-thinking --scope user -- npx -y @modelcontextprotocol/sequential-thinking
# GitHub: claude mcp add github --scope user -e GITHUB_TOKEN=your_token -- npx -y @modelcontextprotocol/github-mcp
# Task Master AI: claude mcp add task-master-ai --scope user -- npx -y task-master-ai

# Verify installation
claude mcp list
```

#### Management Commands
```bash
# List all installed MCP servers
claude mcp list

# Get details about a specific server
claude mcp get perplexity-ask
claude mcp get firecrawl
claude mcp get context7

# Remove a server (if needed)
claude mcp remove perplexity-ask --scope user
claude mcp remove firecrawl --scope user
claude mcp remove context7 --scope user
```

#### Checking MCP Availability in Claude
Use the `/mcp` command in any Claude session to see available MCP tools. If tools are missing, reinstall using the commands above.

### 📊 Current MCP Server Status (July 2025)

#### ✅ Working MCP Servers
1. **MySQL** - Database operations (connect_db, query, execute, list_tables, describe_table)
2. **Sequential Thinking** - Advanced problem-solving and chain of thought reasoning
3. **Perplexity** - AI-powered search (3 models: search, reason, deep_research)
4. **Firecrawl** - Web scraping and crawling (9 functions including batch_scrape)
5. **Context7** - Library documentation (resolve-library-id, get-library-docs)
6. **GitHub** - Complete GitHub API integration (23 functions for repos, issues, PRs)
7. **Task Master AI** - Project task management (41 functions for task tracking)

#### ❌ Not Yet Available
Direct AI model MCP servers for:
- OpenAI (GPT-4, o3, etc.)
- Gemini/Google
- Anthropic Claude
- Groq, XAI, Mistral

**Note**: These attempted installations were removed as the npm packages don't exist yet. For AI model access, use API keys directly in your application.

#### 🔧 Available API Keys
**See**: `~/deployments/CLAUDE.md` line 309 for complete environment variable reference

## 🎯 Model Configuration Notes

### Database-First Model Management (Updated July 10, 2025)

#### 🚨 CRITICAL DATABASE STRUCTURE (DO NOT CHANGE)
- **Database canonicalName INCLUDES provider prefix**: `openai/gpt-4o`, `xai/grok-3`, `gemini/gemini-1.5-pro`
- **This canonicalName is used directly by AI SDK** - no transformation needed!
- **ALL 209 database models now have consistent prefixes** (updated July 10, 2025)

#### Current Status (July 10, 2025 - DATABASE-FIRST ARCHITECTURE COMPLETE!)
- **Database Models**: 209 total (all with provider prefixes)
- **Providers Table**: 21 providers with complete configuration
  - ✅ All 209 models linked to valid providers with foreign key constraints
  - ✅ ProviderRepository with Redis caching (5-minute TTL)
  - ✅ Direct provider SDK integration (no proxy layer)
- **Working Providers**: 21/21 ✅ **ALL PROVIDERS OPERATIONAL!**
  - ✅ OpenAI (direct SDK integration)
  - ✅ Anthropic (direct SDK with x-api-key authentication)
  - ✅ Google/Gemini (direct Google AI SDK)
  - ✅ Perplexity (direct SDK integration)
  - ✅ Groq (direct SDK integration)
  - ✅ xAI (direct SDK integration)
  - ✅ DeepSeek (direct SDK integration)
  - ✅ Mistral (direct SDK integration)
  - ✅ Cohere (direct SDK integration)
  - ✅ OpenRouter (direct API integration)
  - ✅ Together AI (direct SDK integration)
  - ✅ Plus 10 additional providers in Providers table

#### What We Accomplished Today (July 10)
1. **Providers Table**: Created complete schema with 21 providers and full configuration
2. **Foreign Key Constraints**: All 209 models properly linked to valid providers
3. **Repository Pattern**: Implemented ProviderRepository with Redis caching (5-minute TTL)
4. **Direct SDK Integration**: All providers use their specific SDKs (Google SDK, Anthropic SDK, etc.)
5. **Architecture Migration**: **Database-first architecture** with direct provider SDK integration
6. **Database-First Success**: Achieved 100% database-first architecture (21/21 providers)

#### Critical Database Architecture Rules
- **Database Format = AI SDK Format**: Both use `provider/model` (e.g., `openai/gpt-4o`)
- **NO transformation needed**: Database canonicalName maps directly to provider SDKs
- **Consistent prefixes REQUIRED**: 
  - OpenAI: `openai/` (e.g., `openai/gpt-4o`, `openai/dall-e-3`)
  - Anthropic: `anthropic/`
  - Google: `gemini/` (for Google AI SDK)
  - xAI: `xai/` (e.g., `xai/grok-3`, `xai/grok-3-mini`, `xai/grok-2-1212`)
  - Together AI: `together_ai/`
  - OpenRouter: `openrouter/`
  - Alibaba: `alibaba/`
  - All other providers: `provider/`

#### Database-First Provider Configuration
```
Providers Table (21 providers):
openai        ✅ Direct OpenAI SDK integration
anthropic     ✅ Direct Anthropic SDK (x-api-key auth)
google        ✅ Direct Google AI SDK
perplexity    ✅ Direct Perplexity SDK
groq          ✅ Direct Groq SDK
deepseek      ✅ Direct DeepSeek SDK
mistral       ✅ Direct Mistral SDK
cohere        ✅ Direct Cohere SDK
alibaba       ✅ Direct Alibaba API
openrouter    ✅ Direct OpenRouter API
xai           ✅ Direct xAI SDK
together      ✅ Direct Together AI SDK
+ 9 more providers in Providers table
```

### Provider-Specific Configuration

#### xAI Models (FIXED July 2025)
- **Issue**: Authentication was previously not working correctly
- **Solution**: Re-added all xAI models with proper configuration
- **Status**: All 8 xAI models working (grok-3, grok-3-mini, grok-2-1212 series)
- **API Key**: Stored in .env as `XAI_API_KEY` (full key starting with `xai-`)
- **Note**: All authentication handled via direct provider SDKs

#### Together AI Models (FIXED July 2025)
- **Issue**: Credential reference system not working
- **Solution**: Re-added models with proper configuration
- **Status**: All 45 Together AI models working
- **API Key**: Stored as `TOGETHER_API_KEY` in .env
- **Models**: 45 models including Llama, QwQ, Mixtral series
#### Alibaba/Qwen Models Configuration
- Alibaba/Qwen models use `alibaba/` prefix and are accessed via OpenAI-compatible API

## 🚀 Deployment Process

For complete deployment instructions using the v3.2 scripts, see:
**`~/deployments/CLAUDE.md`** - Section "🚀 Deployment Process"

### Quick Reference
- **Staging**: `./deploy-staging-v3.sh` (from develop branch)
- **Production**: `./deploy-production-v3.sh` (from main branch)
- **Timeout**: Always set `export BASH_DEFAULT_TIMEOUT_MS=1200000` first

### Development-Specific Notes
- Model statistics are now generated at runtime (no build-time generation)
- Each environment has separate `.env` and `.env.local` files
- Redis must use `localhost` not `127.0.0.1` in URLs
- All deployment logs saved to `/home/<USER>/deployments/logs/deploy-*.log`

## 🛡️ Infrastructure & Security

### SSL/TLS Configuration
- **Provider**: Let's Encrypt via Certbot
- **Auto-renewal**: Enabled (runs twice daily)
- **Certificates**:
  - Production: `justsimple.chat`, `www.justsimple.chat`
  - Staging: `staging.justsimple.chat`
  - Development: `dev.justsimple.chat`
- **SSL Mode**: Full (strict) when using Cloudflare proxy

### Cloudflare Integration (Implemented July 13, 2025)
- **Real IP Restoration**: Configured via `/etc/nginx/cloudflare.conf`
- **Auto-update**: Weekly cron job updates Cloudflare IP ranges
- **Headers**: Proper WebSocket and streaming support
- **Timeouts**: Extended for long AI model responses (300s read, 75s connect)
- **Documentation**: `/DOCS/cloudflare-nginx-optimizations.md`

### Nginx Optimizations
- **Upstream Keepalive**: 64 connections per environment
- **Streaming Support**: `X-Accel-Buffering` disabled for SSE
- **Configuration**: `/etc/nginx/sites-available/justsimplechat-complete`
- **Logs**: Show real visitor IPs (not Cloudflare IPs)

### PM2 Cluster Mode (Production)
- **Mode**: Cluster with 4 worker processes
- **Zero-downtime**: Enabled via PM2 reload
- **Build**: Standalone Next.js output
- **Config**: Points directly to `.next/standalone/server.js`
- **Memory**: Auto-restart at 1GB limit

### Cache Management
- **Script**: `/usr/local/bin/cloudflare-cache-purge.sh`
- **Usage**: Integrate with deployment scripts
- **Requirements**: `CLOUDFLARE_ZONE_ID` and `CLOUDFLARE_API_TOKEN`

## 📊 Recent Implementations

### Recent Deployment Improvements (July 13, 2025)
✅ **Complete Cloudflare Integration Successfully Tested**
- **Cache Purge**: Automatic cache purging after each deployment
- **Real IP Restoration**: Nginx properly shows visitor IPs (not Cloudflare IPs)  
- **Extended Timeouts**: 300s read, 75s connect (prevents 524 timeout errors)
- **Streaming Support**: Disabled buffering for real-time AI responses
- **Environment Validation**: Enhanced deployment scripts validate all configs
- **Zero-Downtime Deploys**: PM2 cluster mode with automatic rollback
- **Deployment Metrics**: Track timing and success rates

✅ **Staging Deployment Successfully Tested (v1.1)**
- Updated version badge from "Alpha" to "Alpha v1.1"
- All Cloudflare optimizations working in staging
- PM2 ecosystem configuration validated and working
- Health endpoints responding correctly

### Previous Implementations
- **Header Changes (July 12, 2025)**: Changed "BETA" to "ALPHA" in navigation
- **Port Configuration Fix (July 12, 2025)**: Fixed staging port mismatch (3000 → 3005)
- **Cluster Mode Implementation (July 13, 2025)**: Converted production to cluster mode

---
**Environment Implementation**: All environments, configuration, and deployment procedures

IMPORTANT: ALWAYS USE CONTEXT7 TO UNDERSTAND NEW SYNTAX OF AI SDK V5

**Last Updated**: July 13, 2025