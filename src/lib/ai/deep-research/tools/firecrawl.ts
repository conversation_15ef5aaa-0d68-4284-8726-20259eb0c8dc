import { z } from 'zod';
import { tool } from 'ai';
import type { ResearchSource, ToolResult } from '../types';
import { FirecrawlProvider } from '@/lib/ai/search/firecrawl-provider';
import { searchManager } from '@/lib/ai/search';

// Firecrawl tool parameters schema
const firecrawlSchema = z.object({
  query: z.string().describe('The research query or topic'),
  mode: z.enum(['search', 'scrape', 'map', 'deep-research']).describe('Firecrawl operation mode'),
  url: z.string().optional().describe('URL to scrape or map (required for scrape/map modes)'),
  options: z.object({
    maxDepth: z.number().int().min(1).max(5).optional().describe('Maximum crawl depth for deep research'),
    maxUrls: z.number().int().min(1).max(100).optional().describe('Maximum URLs to process'),
    timeLimit: z.number().int().min(30).max(300).optional().describe('Time limit in seconds'),
    onlyMainContent: z.boolean().optional().describe('Extract only main content'),
    formats: z.array(z.enum(['markdown', 'html', 'links'])).optional().describe('Content formats to extract')
  }).optional()
});

export type FirecrawlParams = z.infer<typeof firecrawlSchema>;

/**
 * Execute Firecrawl operations
 * Extracted for reuse in multiple contexts
 */
async function executeFirecrawl(params: FirecrawlParams): Promise<ToolResult> {
    try {
      const apiKey = process.env.FIRECRAWL_API_KEY;
      if (!apiKey) {
        // Fallback to search manager's standard search
        console.warn('[Firecrawl] No API key configured, falling back to standard search');
        const results = await searchManager.search(params.query, { limit: 10 });
        return {
          success: true,
          data: {
            sources: results.map(r => ({
              url: r.url,
              title: r.title,
              snippet: r.snippet,
              relevance: r.score,
              metadata: r.metadata
            })),
            metadata: { fallback: true, mode: 'search' }
          }
        };
      }

      const provider = new FirecrawlProvider(apiKey);
      const mode = params.mode || 'search'; // Default to search mode

      switch (mode) {
        case 'search': {
          // Use Firecrawl's search endpoint
          const results = await provider.search(params.query, {
            limit: params.options?.maxUrls || 10,
            scrapeOptions: {
              formats: params.options?.formats || ['markdown'],
              onlyMainContent: params.options?.onlyMainContent ?? true
            }
          });

          return {
            success: true,
            data: {
              sources: results.map(r => ({
                url: r.url,
                title: r.title,
                snippet: r.snippet,
                relevance: r.score,
                metadata: r.metadata
              })),
              metadata: { mode: 'search', resultsCount: results.length }
            }
          };
        }

        case 'scrape': {
          // Direct URL scraping
          if (!params.url) {
            throw new Error('URL is required for scrape mode');
          }

          const response = await fetch('https://api.firecrawl.dev/v1/scrape', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${apiKey}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              url: params.url,
              formats: params.options?.formats || ['markdown'],
              onlyMainContent: params.options?.onlyMainContent ?? true,
              waitFor: 2000
            })
          });

          if (!response.ok) {
            throw new Error(`Firecrawl scrape failed: ${response.statusText}`);
          }

          const data = await response.json();
          return {
            success: true,
            data: {
              content: data.data?.markdown || data.data?.content || '',
              sources: [{
                url: params.url,
                title: data.data?.metadata?.title || 'Scraped Content',
                snippet: data.data?.metadata?.description || '',
                relevance: 1.0,
                metadata: data.data?.metadata || {}
              }],
              metadata: { mode: 'scrape', formats: params.options?.formats }
            }
          };
        }

        case 'map': {
          // Map website structure
          if (!params.url) {
            throw new Error('URL is required for map mode');
          }

          const response = await fetch('https://api.firecrawl.dev/v1/map', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${apiKey}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              url: params.url,
              search: params.query,
              limit: params.options?.maxUrls || 50
            })
          });

          if (!response.ok) {
            throw new Error(`Firecrawl map failed: ${response.statusText}`);
          }

          const data = await response.json();
          const urls = data.data || [];

          return {
            success: true,
            data: {
              urls,
              sources: urls.slice(0, 10).map((url: string, index: number) => ({
                url,
                title: `Page ${index + 1}`,
                snippet: '',
                relevance: 1 - (index * 0.05),
                metadata: { fromMap: true }
              })),
              metadata: { 
                mode: 'map', 
                totalUrls: urls.length,
                baseUrl: params.url 
              }
            }
          };
        }

        case 'deep-research': {
          // Use existing deep research functionality
          const result = await provider.deepResearch(params.query, {
            maxDepth: params.options?.maxDepth,
            timeLimit: params.options?.timeLimit,
            maxUrls: params.options?.maxUrls
          });

          return {
            success: true,
            data: {
              analysis: result.finalAnalysis,
              sources: result.sources,
              metadata: {
                mode: 'deep-research',
                activitiesPerformed: result.activities?.length || 0
              }
            }
          };
        }

        default:
          throw new Error(`Unknown Firecrawl mode: ${params.mode}`);
      }
    } catch (error) {
      console.error('[Firecrawl] Error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        data: null
      };
    }
}

/**
 * Firecrawl Tool for comprehensive web scraping and research
 * Integrates with existing FirecrawlProvider in the codebase
 */
export const firecrawlTool = tool({
  description: 'Advanced web scraping and deep research using Firecrawl API for comprehensive content extraction',
  parameters: firecrawlSchema,
  execute: async (params, options) => executeFirecrawl(params)
});

/**
 * Scrape multiple URLs in parallel using Firecrawl
 */
export async function batchScrapeUrls(
  urls: string[],
  options?: {
    formats?: ('markdown' | 'html' | 'links')[];
    onlyMainContent?: boolean;
    maxConcurrent?: number;
  }
): Promise<Array<{ url: string; content: string; metadata: any }>> {
  const apiKey = process.env.FIRECRAWL_API_KEY;
  if (!apiKey) {
    throw new Error('Firecrawl API key not configured');
  }

  const maxConcurrent = options?.maxConcurrent || 5;
  const results: Array<{ url: string; content: string; metadata: any }> = [];

  // Process URLs in batches
  for (let i = 0; i < urls.length; i += maxConcurrent) {
    const batch = urls.slice(i, i + maxConcurrent);
    const batchPromises = batch.map(async (url) => {
      try {
        const result = await executeFirecrawl({
          query: '',
          mode: 'scrape',
          url,
          options: {
            formats: options?.formats || ['markdown'],
            onlyMainContent: options?.onlyMainContent ?? true
          }
        });

        if (result.success && result.data) {
          return {
            url,
            content: result.data.content || '',
            metadata: result.data.sources?.[0]?.metadata || {}
          };
        }
        return null;
      } catch (error) {
        console.error(`[Firecrawl] Failed to scrape ${url}:`, error);
        return null;
      }
    });

    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults.filter(Boolean) as any[]);
  }

  return results;
}

/**
 * Extract structured data from a webpage using Firecrawl
 */
export async function extractStructuredData(
  url: string,
  schema: Record<string, any>
): Promise<any> {
  const apiKey = process.env.FIRECRAWL_API_KEY;
  if (!apiKey) {
    throw new Error('Firecrawl API key not configured');
  }

  try {
    const response = await fetch('https://api.firecrawl.dev/v1/scrape', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        url,
        formats: ['extract'],
        extract: {
          schema,
          systemPrompt: 'Extract the requested information accurately from the webpage.'
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Firecrawl extract failed: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data?.extract || null;
  } catch (error) {
    console.error('[Firecrawl] Extract error:', error);
    throw error;
  }
}

/**
 * Research a topic using Firecrawl's comprehensive crawling
 */
export async function comprehensiveWebResearch(
  topic: string,
  startUrls?: string[]
): Promise<{
  sources: ResearchSource[];
  keyInsights: string[];
  relatedTopics: string[];
}> {
  try {
    // First, search for relevant URLs if none provided
    if (!startUrls || startUrls.length === 0) {
      const searchResult = await executeFirecrawl({
        query: topic,
        mode: 'search',
        options: { maxUrls: 20 }
      });

      if (searchResult.success && searchResult.data?.sources) {
        startUrls = searchResult.data.sources.slice(0, 5).map((s: any) => s.url);
      } else {
        throw new Error('Failed to find relevant URLs for research');
      }
    }

    // Scrape the top URLs
    const scrapedContent = await batchScrapeUrls(startUrls!, {
      onlyMainContent: true,
      maxConcurrent: 3
    });

    // Extract key insights from content
    const keyInsights: string[] = [];
    const relatedTopics: string[] = [];
    const sources: ResearchSource[] = [];

    for (const { url, content, metadata } of scrapedContent) {
      // Create source entry
      sources.push({
        url,
        title: metadata.title || 'Research Source',
        snippet: content.substring(0, 200) + '...',
        relevance: 0.8,
        metadata: {
          ...metadata,
          contentLength: content.length
        }
      });

      // Extract insights (simplified - in production would use AI)
      const sentences = content.split('. ').filter(s => s.length > 50);
      keyInsights.push(...sentences.slice(0, 2));
    }

    return {
      sources,
      keyInsights: [...new Set(keyInsights)].slice(0, 10),
      relatedTopics
    };
  } catch (error) {
    console.error('[Firecrawl] Comprehensive research error:', error);
    throw error;
  }
}