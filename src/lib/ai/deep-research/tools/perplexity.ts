import { z } from 'zod';
import { tool } from 'ai';
import type { ResearchSource, ToolResult } from '../types';

// Perplexity API configuration
const PERPLEXITY_API_KEY = process.env.PERPLEXITY_API_KEY || '';
const PERPLEXITY_API_URL = 'https://api.perplexity.ai/chat/completions';

// Perplexity models
export enum PerplexityModel {
  SONAR_PRO = 'sonar-pro-latest',           // Best for complex queries
  SONAR = 'sonar-latest',                   // Standard search
  SONAR_REASONING = 'sonar-reasoning-latest' // Multi-step reasoning
}

// Search focus types
export enum SearchFocus {
  INTERNET = 'internet',     // General web search
  ACADEMIC = 'academic',     // Academic papers
  WRITING = 'writing',       // Writing assistance
  YOUTUBE = 'youtube',       // YouTube transcripts
  REDDIT = 'reddit'          // Reddit discussions
}

// Perplexity search parameters schema
const perplexitySearchSchema = z.object({
  query: z.string().describe('The research query or question'),
  model: z.nativeEnum(PerplexityModel).describe('Perplexity model to use'),
  search_focus: z.nativeEnum(SearchFocus).optional().describe('Focus search on specific content types'),
  search_recency: z.enum(['day', 'week', 'month', 'year']).optional().describe('Filter by content recency'),
  max_tokens: z.number().int().min(100).max(4000).describe('Maximum tokens in response'),
  temperature: z.number().min(0).max(1).describe('Response randomness (0=focused, 1=creative)'),
  return_citations: z.boolean().describe('Include source citations in response'),
  return_related_questions: z.boolean().describe('Generate related questions')
});

export type PerplexitySearchParams = z.infer<typeof perplexitySearchSchema>;

// Response interfaces
interface PerplexityCitation {
  url: string;
  title?: string;
  snippet?: string;
  index: number;
}

interface PerplexityResponse {
  id: string;
  model: string;
  created: number;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  citations?: string[];
  choices: Array<{
    index: number;
    finish_reason: string;
    message: {
      role: string;
      content: string;
    };
    delta?: {
      role?: string;
      content?: string;
    };
  }>;
}

/**
 * Execute Perplexity search operations
 * Extracted for reuse in multiple contexts
 */
async function executePerplexity(params: PerplexitySearchParams): Promise<ToolResult> {
    try {
      if (!PERPLEXITY_API_KEY) {
        throw new Error('Perplexity API key not configured');
      }

      // Apply defaults
      const model = params.model || PerplexityModel.SONAR;
      const max_tokens = params.max_tokens || 2000;
      const temperature = params.temperature || 0.2;
      const return_citations = params.return_citations ?? true;
      const return_related_questions = params.return_related_questions ?? true;

      // Build request body
      const requestBody = {
        model,
        messages: [
          {
            role: 'system',
            content: 'You are a helpful research assistant. Provide comprehensive, accurate information with citations.'
          },
          {
            role: 'user',
            content: params.query
          }
        ],
        max_tokens,
        temperature,
        return_citations,
        return_related_questions,
        search_domain_filter: params.search_focus ? [params.search_focus] : undefined,
        search_recency_filter: params.search_recency
      };

      // Make API request
      const response = await fetch(PERPLEXITY_API_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${PERPLEXITY_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Perplexity API error: ${response.status} - ${errorText}`);
      }

      const data: PerplexityResponse = await response.json();

      // Extract content and citations
      const content = data.choices[0]?.message?.content || '';
      const citations = data.citations || [];

      // Parse citations from content (if embedded)
      const sources: ResearchSource[] = [];
      if (citations.length > 0) {
        citations.forEach((citation, index) => {
          // Extract URL and title from citation string
          const urlMatch = citation.match(/https?:\/\/[^\s]+/);
          if (urlMatch) {
            sources.push({
              url: urlMatch[0],
              title: citation.replace(urlMatch[0], '').trim() || `Source ${index + 1}`,
              snippet: '',
              relevance: 0.9 - (index * 0.05),
              metadata: {
                citation_index: index,
                from_perplexity: true
              }
            });
          }
        });
      }

      // Extract related questions if present
      const relatedQuestions: string[] = [];
      const relatedMatch = content.match(/Related questions?:([\s\S]*?)(?:\n\n|$)/);
      if (relatedMatch) {
        const questions = relatedMatch[1].split('\n').map(q => q.trim().replace(/^[-•*]\s*/, ''));
        relatedQuestions.push(...questions.filter(q => q.length > 0));
      }

      return {
        success: true,
        data: {
          content,
          sources,
          metadata: {
            model,
            tokens_used: data.usage.total_tokens,
            related_questions: relatedQuestions,
            search_focus: params.search_focus
          }
        }
      };
    } catch (error) {
      console.error('[Perplexity] Error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        data: null
      };
    }
}

/**
 * Perplexity AI Tool for advanced research queries
 * FALLBACK OPTION: Used when primary tools (Brave Search, Firecrawl) cannot find sufficient information
 * Provides access to Perplexity's search-augmented language models
 * Note: Perplexity is itself a deep research tool, so we use it as a last resort
 */
export const perplexityTool = tool({
  description: 'FALLBACK: Advanced AI-powered research using Perplexity when primary tools cannot find sufficient information',
  parameters: perplexitySearchSchema,
  execute: async (params, options) => executePerplexity(params)
});

/**
 * Perform deep research using Perplexity's reasoning model
 */
export async function deepResearchWithPerplexity(
  query: string,
  options?: Partial<PerplexitySearchParams>
): Promise<{
  analysis: string;
  sources: ResearchSource[];
  related_questions: string[];
}> {
  const result = await executePerplexity({
    query,
    model: PerplexityModel.SONAR_REASONING,
    max_tokens: 3000,
    temperature: 0.1,
    return_citations: true,
    return_related_questions: true,
    ...options
  });

  if (result.success && result.data) {
    return {
      analysis: result.data.content,
      sources: result.data.sources || [],
      related_questions: result.data.metadata?.related_questions || []
    };
  }

  return {
    analysis: '',
    sources: [],
    related_questions: []
  };
}

/**
 * Search academic papers using Perplexity
 */
export async function searchAcademicPapers(
  topic: string,
  recency?: 'year' | 'month'
): Promise<{
  papers: ResearchSource[];
  summary: string;
}> {
  const result = await executePerplexity({
    query: `Academic research papers on: ${topic}. Include paper titles, authors, and key findings.`,
    model: PerplexityModel.SONAR_PRO,
    search_focus: SearchFocus.ACADEMIC,
    search_recency: recency,
    max_tokens: 2500,
    temperature: 0.1,
    return_citations: true,
    return_related_questions: true
  });

  if (result.success && result.data) {
    return {
      papers: result.data.sources || [],
      summary: result.data.content
    };
  }

  return {
    papers: [],
    summary: ''
  };
}

/**
 * Analyze Reddit discussions on a topic
 */
export async function analyzeRedditDiscussions(
  topic: string
): Promise<{
  insights: string;
  sources: ResearchSource[];
  sentiment: 'positive' | 'negative' | 'mixed' | 'neutral';
}> {
  const result = await executePerplexity({
    query: `Analyze Reddit discussions about: ${topic}. Include community sentiment, main concerns, and popular opinions.`,
    model: PerplexityModel.SONAR,
    search_focus: SearchFocus.REDDIT,
    search_recency: 'month',
    max_tokens: 2000,
    temperature: 0.2,
    return_citations: true,
    return_related_questions: true
  });

  if (result.success && result.data) {
    // Simple sentiment analysis from content
    const content = result.data.content.toLowerCase();
    let sentiment: 'positive' | 'negative' | 'mixed' | 'neutral' = 'neutral';
    
    const positiveWords = ['positive', 'great', 'excellent', 'love', 'amazing', 'helpful'];
    const negativeWords = ['negative', 'bad', 'terrible', 'hate', 'awful', 'useless'];
    
    const positiveCount = positiveWords.filter(word => content.includes(word)).length;
    const negativeCount = negativeWords.filter(word => content.includes(word)).length;
    
    if (positiveCount > negativeCount * 2) sentiment = 'positive';
    else if (negativeCount > positiveCount * 2) sentiment = 'negative';
    else if (positiveCount > 0 && negativeCount > 0) sentiment = 'mixed';

    return {
      insights: result.data.content,
      sources: result.data.sources || [],
      sentiment
    };
  }

  return {
    insights: '',
    sources: [],
    sentiment: 'neutral'
  };
}

/**
 * Multi-model ensemble research combining different Perplexity models
 */
export async function ensembleResearch(
  query: string
): Promise<{
  comprehensive_analysis: string;
  quick_summary: string;
  reasoning_analysis: string;
  all_sources: ResearchSource[];
}> {
  // Run all three models in parallel
  const [proResult, standardResult, reasoningResult] = await Promise.allSettled([
    executePerplexity({
      query,
      model: PerplexityModel.SONAR_PRO,
      max_tokens: 2500,
      temperature: 0.2,
      return_citations: true,
      return_related_questions: true
    }),
    executePerplexity({
      query,
      model: PerplexityModel.SONAR,
      max_tokens: 1500,
      temperature: 0.2,
      return_citations: true,
      return_related_questions: true
    }),
    executePerplexity({
      query,
      model: PerplexityModel.SONAR_REASONING,
      max_tokens: 3000,
      temperature: 0.2,
      return_citations: true,
      return_related_questions: true
    })
  ]);

  // Aggregate results
  const allSources = new Map<string, ResearchSource>();
  let comprehensive = '';
  let quick = '';
  let reasoning = '';

  if (proResult.status === 'fulfilled' && proResult.value.success && proResult.value.data) {
    comprehensive = proResult.value.data.content;
    proResult.value.data.sources?.forEach((s: ResearchSource) => allSources.set(s.url, s));
  }

  if (standardResult.status === 'fulfilled' && standardResult.value.success && standardResult.value.data) {
    quick = standardResult.value.data.content;
    standardResult.value.data.sources?.forEach((s: ResearchSource) => {
      if (!allSources.has(s.url)) allSources.set(s.url, s);
    });
  }

  if (reasoningResult.status === 'fulfilled' && reasoningResult.value.success && reasoningResult.value.data) {
    reasoning = reasoningResult.value.data.content;
    reasoningResult.value.data.sources?.forEach((s: ResearchSource) => {
      if (!allSources.has(s.url)) allSources.set(s.url, s);
    });
  }

  return {
    comprehensive_analysis: comprehensive,
    quick_summary: quick,
    reasoning_analysis: reasoning,
    all_sources: Array.from(allSources.values())
  };
}