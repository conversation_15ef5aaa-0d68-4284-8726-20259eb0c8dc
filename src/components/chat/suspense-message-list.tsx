'use client';

import { Suspense, startTransition, useMemo } from 'react';
import { Message, MessageRole } from '@/types';
import { MessageList } from './message-list';
import { MessageSkeleton, ConversationSkeleton, StreamingSkeleton } from './message-skeleton';
import { ErrorBoundary } from '@/components/ui/error-boundary';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';

interface SuspenseMessageListProps {
  messages: Message[];
  streamingMessage?: Message | null;
  onRetryMessage?: (messageId: string) => void;
  webSearchActive?: boolean;
  webSearchQuery?: string;
  webSearchQueries?: string[];
  webSearchResults?: number;
  isLoading: boolean;
  isProcessingAttachments: boolean;
  currentAssistantMessage: string;
  toolCalls?: any[];
  activeToolCalls?: any[];
  onOpenCanvas?: (code: string) => void;
}

// Lazy-loaded message list component
function LazyMessageList(props: SuspenseMessageListProps) {
  // Use useMemo to prevent unnecessary re-renders during streaming
  const memoizedMessages = useMemo(() => props.messages, [props.messages]);
  
  return (
    <MessageList 
      {...props}
      messages={memoizedMessages}
    />
  );
}

// Error fallback component
function MessageListErrorFallback({ 
  error, 
  resetErrorBoundary 
}: { 
  error: Error; 
  resetErrorBoundary: () => void; 
}) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex flex-col items-center justify-center p-8 space-y-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800"
    >
      <AlertCircle className="w-8 h-8 text-red-500" />
      <div className="text-center space-y-2">
        <h3 className="font-semibold text-red-900 dark:text-red-100">
          Failed to load conversation
        </h3>
        <p className="text-sm text-red-700 dark:text-red-300">
          {error.message || 'Something went wrong while loading the messages.'}
        </p>
      </div>
      <Button
        onClick={resetErrorBoundary}
        variant="outline"
        size="sm"
        className="gap-2"
      >
        <RefreshCw className="w-4 h-4" />
        Try again
      </Button>
    </motion.div>
  );
}

// Intelligent loading fallback based on state
function MessageListSuspenseFallback({ 
  isLoading, 
  isProcessingAttachments,
  hasMessages 
}: { 
  isLoading: boolean;
  isProcessingAttachments: boolean;
  hasMessages: boolean;
}) {
  if (isProcessingAttachments) {
    return (
      <div className="space-y-4">
        <ConversationSkeleton messageCount={2} />
        <StreamingSkeleton />
      </div>
    );
  }

  if (isLoading && !hasMessages) {
    return <ConversationSkeleton messageCount={3} />;
  }

  if (isLoading && hasMessages) {
    return <StreamingSkeleton />;
  }

  // Default fallback for general loading
  return <ConversationSkeleton messageCount={2} />;
}

export function SuspenseMessageList(props: SuspenseMessageListProps) {
  const { messages, isLoading, isProcessingAttachments } = props;

  return (
    <ErrorBoundary
      fallback={MessageListErrorFallback}
      onReset={() => {
        // Optionally trigger a refetch or reset state
        console.log('Message list error boundary reset');
      }}
    >
      <Suspense
        fallback={
          <MessageListSuspenseFallback
            isLoading={isLoading}
            isProcessingAttachments={isProcessingAttachments}
            hasMessages={messages.length > 0}
          />
        }
      >
        <LazyMessageList {...props} />
      </Suspense>
    </ErrorBoundary>
  );
}

// Hook for optimistic message updates with React 18 concurrent features
export function useOptimisticMessages(messages: Message[]) {
  return useMemo(() => {
    // This can be expanded to include optimistic updates
    return messages;
  }, [messages]);
}

// Utility function for smooth message transitions
export function updateMessagesWithTransition(
  updateFn: () => void
) {
  startTransition(() => {
    updateFn();
  });
}