import { Redis } from 'ioredis';
import { EventEmitter } from 'events';
import type { DeepResearchRequest, DeepResearchResult, ResearchSession } from '../types';
import { DeepResearchOrchestrator } from '../orchestrator';
import { routerLogger } from '@/lib/logger';

/**
 * Research job structure for queue
 */
export interface ResearchJob {
  id: string;
  request: DeepResearchRequest;
  session: ResearchSession;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  attempts: number;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
  result?: DeepResearchResult;
  workerId?: string;
}

/**
 * Research Queue Manager using Redis
 * Provides reliable task processing across servers and restarts
 */
export class ResearchQueueManager extends EventEmitter {
  private redis: Redis;
  private subscriber: Redis;
  private workerId: string;
  private isProcessing: boolean = false;
  private currentJob: ResearchJob | null = null;
  private checkInterval: NodeJS.Timeout | null = null;
  private readonly QUEUE_KEY = 'deep-research:queue';
  private readonly JOBS_KEY = 'deep-research:jobs';
  private readonly PROCESSING_KEY = 'deep-research:processing';
  private readonly COMPLETED_KEY = 'deep-research:completed';
  private readonly FAILED_KEY = 'deep-research:failed';
  private readonly WORKER_HEARTBEAT_KEY = 'deep-research:workers';
  private readonly JOB_TIMEOUT = 1800000; // 30 minutes
  private readonly HEARTBEAT_INTERVAL = 30000; // 30 seconds
  private heartbeatInterval: NodeJS.Timeout | null = null;

  constructor(redisConfig?: { host?: string; port?: number; password?: string }) {
    super();
    
    // Initialize Redis clients
    this.redis = new Redis({
      host: redisConfig?.host || process.env.REDIS_HOST || 'localhost',
      port: redisConfig?.port || parseInt(process.env.REDIS_PORT || '6379'),
      password: redisConfig?.password || process.env.REDIS_PASSWORD,
      maxRetriesPerRequest: 3,
      enableReadyCheck: true,
      retryStrategy: (times: number) => {
        const delay = Math.min(times * 50, 2000);
        return delay;
      }
    });

    // Separate client for subscriptions
    this.subscriber = this.redis.duplicate();
    
    // Generate unique worker ID
    this.workerId = `worker_${process.pid}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Set up event handlers
    this.setupEventHandlers();
    
    console.log(`[ResearchQueue] Worker ${this.workerId} initialized`);
  }

  /**
   * Enqueue a new research job
   */
  async enqueueJob(
    request: DeepResearchRequest,
    session: ResearchSession
  ): Promise<string> {
    const job: ResearchJob = {
      id: session.id,
      request,
      session,
      status: 'pending',
      attempts: 0,
      createdAt: new Date()
    };

    try {
      // Store job data
      await this.redis.hset(
        this.JOBS_KEY,
        job.id,
        JSON.stringify(job)
      );

      // Add to queue
      await this.redis.rpush(this.QUEUE_KEY, job.id);

      // Publish event
      await this.redis.publish('deep-research:new-job', job.id);

      this.emit('job:enqueued', job);
      console.log(`[ResearchQueue] Job ${job.id} enqueued`);

      return job.id;
    } catch (error) {
      console.error('[ResearchQueue] Failed to enqueue job:', error);
      throw error;
    }
  }

  /**
   * Start processing jobs
   */
  async startWorker(): Promise<void> {
    if (this.isProcessing) {
      console.log('[ResearchQueue] Worker already running');
      return;
    }

    this.isProcessing = true;
    console.log(`[ResearchQueue] Starting worker ${this.workerId}`);

    // Start heartbeat
    this.startHeartbeat();

    // Subscribe to new job notifications
    await this.subscriber.subscribe('deep-research:new-job');
    this.subscriber.on('message', () => {
      if (!this.currentJob) {
        this.processNextJob().catch(console.error);
      }
    });

    // Check for stale jobs periodically
    this.checkInterval = setInterval(() => {
      this.recoverStaleJobs().catch(console.error);
    }, 60000); // Every minute

    // Start processing
    await this.processNextJob();
  }

  /**
   * Stop processing jobs
   */
  async stopWorker(): Promise<void> {
    console.log(`[ResearchQueue] Stopping worker ${this.workerId}`);
    this.isProcessing = false;

    // Stop intervals
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }

    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    // Unsubscribe
    await this.subscriber.unsubscribe();

    // Remove worker from active list
    await this.redis.hdel(this.WORKER_HEARTBEAT_KEY, this.workerId);

    // Return current job to queue if any
    if (this.currentJob) {
      await this.requeueJob(this.currentJob);
    }
  }

  /**
   * Process next job from queue
   */
  private async processNextJob(): Promise<void> {
    if (!this.isProcessing || this.currentJob) {
      return;
    }

    try {
      // Get next job from queue
      const jobId = await this.redis.lpop(this.QUEUE_KEY);
      if (!jobId) {
        // No jobs available
        setTimeout(() => {
          if (this.isProcessing && !this.currentJob) {
            this.processNextJob().catch(console.error);
          }
        }, 5000); // Check again in 5 seconds
        return;
      }

      // Get job data
      const jobData = await this.redis.hget(this.JOBS_KEY, jobId);
      if (!jobData) {
        console.error(`[ResearchQueue] Job ${jobId} not found`);
        return this.processNextJob();
      }

      const job: ResearchJob = JSON.parse(jobData);
      job.startedAt = new Date();
      job.status = 'processing';
      job.workerId = this.workerId;
      job.attempts++;

      // Move to processing set
      await this.redis.hset(this.PROCESSING_KEY, jobId, JSON.stringify({
        workerId: this.workerId,
        startedAt: job.startedAt.toISOString()
      }));

      // Update job
      await this.redis.hset(this.JOBS_KEY, jobId, JSON.stringify(job));

      this.currentJob = job;
      this.emit('job:started', job);
      console.log(`[ResearchQueue] Processing job ${jobId} (attempt ${job.attempts})`);

      // Process the job
      try {
        const orchestrator = new DeepResearchOrchestrator();
        const result = await orchestrator.streamingResearch(
          job.session,
          job.request,
          {
            onProgress: async (update) => {
              // Store progress events
              await this.redis.publish(`deep-research:progress:${jobId}`, JSON.stringify(update));
              this.emit('job:progress', { jobId, event: update });
            },
            onComplete: async (result) => {
              // Job completed successfully
              job.completedAt = new Date();
              job.status = 'completed';
              job.result = result;

              // Move to completed set
              await this.redis.hdel(this.PROCESSING_KEY, jobId);
              await this.redis.hset(this.COMPLETED_KEY, jobId, JSON.stringify({
                completedAt: job.completedAt.toISOString(),
                workerId: this.workerId
              }));

              // Update job
              await this.redis.hset(this.JOBS_KEY, jobId, JSON.stringify(job));

              // Publish completion
              await this.redis.publish(`deep-research:complete:${jobId}`, JSON.stringify(result));

              this.emit('job:completed', job);
              console.log(`[ResearchQueue] Job ${jobId} completed successfully`);
            },
            onError: async (error) => {
              throw error; // Will be caught by outer try-catch
            }
          }
        );

        // Mark as complete
        this.currentJob = null;
        
        // Process next job
        setImmediate(() => {
          this.processNextJob().catch(console.error);
        });
      } catch (error) {
        // Job failed
        console.error(`[ResearchQueue] Job ${jobId} failed:`, error);
        
        job.error = error instanceof Error ? error.message : 'Unknown error';
        
        if (job.attempts < 3) {
          // Retry the job
          await this.requeueJob(job);
          this.emit('job:retry', job);
          console.log(`[ResearchQueue] Job ${jobId} will be retried (${job.attempts}/3)`);
        } else {
          // Move to failed set
          job.status = 'failed';
          job.completedAt = new Date();
          
          await this.redis.hdel(this.PROCESSING_KEY, jobId);
          await this.redis.hset(this.FAILED_KEY, jobId, JSON.stringify({
            failedAt: job.completedAt.toISOString(),
            error: job.error,
            workerId: this.workerId
          }));
          
          await this.redis.hset(this.JOBS_KEY, jobId, JSON.stringify(job));
          await this.redis.publish(`deep-research:failed:${jobId}`, job.error);
          
          this.emit('job:failed', job);
          console.error(`[ResearchQueue] Job ${jobId} failed after ${job.attempts} attempts`);
        }
        
        this.currentJob = null;
        
        // Process next job
        setImmediate(() => {
          this.processNextJob().catch(console.error);
        });
      }
    } catch (error) {
      console.error('[ResearchQueue] Error processing job:', error);
      this.currentJob = null;
      
      // Try again after delay
      setTimeout(() => {
        this.processNextJob().catch(console.error);
      }, 5000);
    }
  }

  /**
   * Requeue a job
   */
  private async requeueJob(job: ResearchJob): Promise<void> {
    job.status = 'pending';
    job.startedAt = undefined;
    job.workerId = undefined;
    
    await this.redis.hset(this.JOBS_KEY, job.id, JSON.stringify(job));
    await this.redis.rpush(this.QUEUE_KEY, job.id);
    await this.redis.hdel(this.PROCESSING_KEY, job.id);
  }

  /**
   * Recover stale jobs from crashed workers
   */
  private async recoverStaleJobs(): Promise<void> {
    try {
      const processingJobs = await this.redis.hgetall(this.PROCESSING_KEY);
      
      for (const [jobId, data] of Object.entries(processingJobs)) {
        const { workerId, startedAt } = JSON.parse(data);
        
        // Check if worker is still alive
        const workerHeartbeat = await this.redis.hget(this.WORKER_HEARTBEAT_KEY, workerId);
        if (workerHeartbeat) {
          const lastHeartbeat = new Date(workerHeartbeat).getTime();
          if (Date.now() - lastHeartbeat < 60000) {
            continue; // Worker is still alive
          }
        }
        
        // Check if job has timed out
        const jobStartTime = new Date(startedAt).getTime();
        if (Date.now() - jobStartTime > this.JOB_TIMEOUT) {
          console.log(`[ResearchQueue] Recovering stale job ${jobId} from worker ${workerId}`);
          
          // Get job and requeue
          const jobData = await this.redis.hget(this.JOBS_KEY, jobId);
          if (jobData) {
            const job: ResearchJob = JSON.parse(jobData);
            await this.requeueJob(job);
            this.emit('job:recovered', job);
          }
        }
      }
    } catch (error) {
      console.error('[ResearchQueue] Error recovering stale jobs:', error);
    }
  }

  /**
   * Start worker heartbeat
   */
  private startHeartbeat(): void {
    // Initial heartbeat
    this.redis.hset(
      this.WORKER_HEARTBEAT_KEY,
      this.workerId,
      new Date().toISOString()
    ).catch(console.error);

    // Regular heartbeat
    this.heartbeatInterval = setInterval(() => {
      this.redis.hset(
        this.WORKER_HEARTBEAT_KEY,
        this.workerId,
        new Date().toISOString()
      ).catch(console.error);
    }, this.HEARTBEAT_INTERVAL);
  }

  /**
   * Get job status
   */
  async getJobStatus(jobId: string): Promise<ResearchJob | null> {
    const jobData = await this.redis.hget(this.JOBS_KEY, jobId);
    if (!jobData) {
      return null;
    }
    return JSON.parse(jobData);
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<{
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    workers: number;
  }> {
    const [pending, processing, completed, failed, workers] = await Promise.all([
      this.redis.llen(this.QUEUE_KEY),
      this.redis.hlen(this.PROCESSING_KEY),
      this.redis.hlen(this.COMPLETED_KEY),
      this.redis.hlen(this.FAILED_KEY),
      this.redis.hlen(this.WORKER_HEARTBEAT_KEY)
    ]);

    return { pending, processing, completed, failed, workers };
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    this.redis.on('error', (error) => {
      console.error('[ResearchQueue] Redis error:', error);
      this.emit('error', error);
    });

    this.redis.on('connect', () => {
      console.log('[ResearchQueue] Connected to Redis');
      this.emit('connected');
    });

    this.redis.on('disconnect', () => {
      console.log('[ResearchQueue] Disconnected from Redis');
      this.emit('disconnected');
    });

    // Graceful shutdown
    process.on('SIGTERM', () => {
      console.log('[ResearchQueue] Received SIGTERM, shutting down gracefully');
      this.stopWorker().then(() => {
        process.exit(0);
      });
    });

    process.on('SIGINT', () => {
      console.log('[ResearchQueue] Received SIGINT, shutting down gracefully');
      this.stopWorker().then(() => {
        process.exit(0);
      });
    });
  }

  /**
   * Clean up completed and failed jobs older than specified time
   */
  async cleanupOldJobs(olderThanMs: number = 86400000): Promise<number> {
    let cleaned = 0;
    const cutoffTime = Date.now() - olderThanMs;

    // Clean completed jobs
    const completedJobs = await this.redis.hgetall(this.COMPLETED_KEY);
    for (const [jobId, data] of Object.entries(completedJobs)) {
      const { completedAt } = JSON.parse(data);
      if (new Date(completedAt).getTime() < cutoffTime) {
        await this.redis.hdel(this.COMPLETED_KEY, jobId);
        await this.redis.hdel(this.JOBS_KEY, jobId);
        cleaned++;
      }
    }

    // Clean failed jobs
    const failedJobs = await this.redis.hgetall(this.FAILED_KEY);
    for (const [jobId, data] of Object.entries(failedJobs)) {
      const { failedAt } = JSON.parse(data);
      if (new Date(failedAt).getTime() < cutoffTime) {
        await this.redis.hdel(this.FAILED_KEY, jobId);
        await this.redis.hdel(this.JOBS_KEY, jobId);
        cleaned++;
      }
    }

    console.log(`[ResearchQueue] Cleaned up ${cleaned} old jobs`);
    return cleaned;
  }

  /**
   * Close connections
   */
  async close(): Promise<void> {
    await this.stopWorker();
    await this.redis.quit();
    await this.subscriber.quit();
  }
}

// Export singleton instance for API usage
export const researchQueue = new ResearchQueueManager();