/**
 * Expert Selector for UltraThink
 * Automatically detects research domain and selects appropriate expert role
 */

import { ExpertRole, ResearchDomain } from './prompt-types';
import { routerLogger } from '@/lib/logger';

/**
 * Domain-specific expert configurations
 */
const EXPERT_ROLES: Record<ResearchDomain, ExpertRole> = {
  technology: {
    type: 'Senior Technology Analyst',
    experience: '15 years',
    domain: 'Technology & Innovation',
    technicalSkills: 'Software architecture, emerging technologies, system design, AI/ML, cybersecurity, cloud computing',
    domainKnowledge: 'Technology adoption cycles, market disruption patterns, technical feasibility assessment, innovation trends',
    methodologicalApproach: 'Evidence-based analysis with technical depth, prototype evaluation, scalability assessment',
    industryInsights: 'Startup ecosystem dynamics, enterprise technology adoption, regulatory technology impact'
  },

  business: {
    type: 'Management Consultant',
    experience: '12 years',
    domain: 'Strategic Business Analysis',
    technicalSkills: 'Financial modeling, market analysis, competitive intelligence, operational optimization, strategic planning',
    domainKnowledge: 'Business model innovation, market entry strategies, organizational transformation, value chain analysis',
    methodologicalApproach: 'Framework-driven analysis using <PERSON><PERSON><PERSON><PERSON><PERSON>, BCG methodologies, data-driven decision making',
    industryInsights: 'Cross-industry best practices, merger & acquisition trends, digital transformation strategies'
  },

  science: {
    type: 'Research Scientist',
    experience: '10 years',
    domain: 'Scientific Research & Development',
    technicalSkills: 'Research methodology, statistical analysis, experimental design, peer review, publication standards',
    domainKnowledge: 'Scientific literature, research trends, grant funding, academic collaboration, innovation pipelines',
    methodologicalApproach: 'Systematic literature review, hypothesis-driven analysis, evidence hierarchy evaluation',
    industryInsights: 'Academic-industry partnerships, technology transfer, research commercialization pathways'
  },

  finance: {
    type: 'Investment Analyst',
    experience: '8 years',
    domain: 'Financial Markets & Investment',
    technicalSkills: 'Financial modeling, valuation methods, risk assessment, portfolio analysis, market research',
    domainKnowledge: 'Capital markets, investment strategies, financial instruments, economic indicators, regulatory environment',
    methodologicalApproach: 'Quantitative analysis with fundamental research, scenario modeling, comparative valuation',
    industryInsights: 'Market cycles, institutional investment trends, regulatory impact, emerging asset classes'
  },

  healthcare: {
    type: 'Healthcare Industry Analyst',
    experience: '12 years',
    domain: 'Healthcare & Life Sciences',
    technicalSkills: 'Clinical research, regulatory compliance, health economics, medical device assessment, pharma analysis',
    domainKnowledge: 'Drug development, medical technology, healthcare delivery, regulatory pathways, reimbursement systems',
    methodologicalApproach: 'Evidence-based medicine principles, regulatory pathway analysis, health technology assessment',
    industryInsights: 'Healthcare policy trends, digital health adoption, pharmaceutical market dynamics, clinical practice evolution'
  },

  energy: {
    type: 'Energy Sector Analyst',
    experience: '10 years',
    domain: 'Energy & Environmental Systems',
    technicalSkills: 'Energy systems analysis, environmental impact assessment, renewable technology, grid analysis, policy evaluation',
    domainKnowledge: 'Energy markets, sustainability metrics, climate policy, technology deployment, infrastructure planning',
    methodologicalApproach: 'Systems thinking approach, lifecycle analysis, techno-economic evaluation, policy impact assessment',
    industryInsights: 'Energy transition dynamics, regulatory frameworks, technology cost curves, market transformation patterns'
  },

  automotive: {
    type: 'Automotive Industry Expert',
    experience: '11 years',
    domain: 'Automotive & Transportation',
    technicalSkills: 'Vehicle engineering, autonomous systems, electric powertrains, manufacturing processes, supply chain analysis',
    domainKnowledge: 'Automotive market trends, regulatory compliance, technology integration, consumer behavior, mobility ecosystems',
    methodologicalApproach: 'Engineering-first analysis, market validation, technology roadmap assessment, competitive benchmarking',
    industryInsights: 'OEM strategies, supplier dynamics, mobility-as-a-service trends, electrification pathways'
  },

  aerospace: {
    type: 'Aerospace Industry Analyst',
    experience: '9 years',
    domain: 'Aerospace & Defense',
    technicalSkills: 'Aerospace engineering, systems integration, certification processes, defense technology, space systems',
    domainKnowledge: 'Aerospace markets, regulatory frameworks, technology development cycles, defense procurement, space economy',
    methodologicalApproach: 'Technical feasibility analysis, regulatory compliance assessment, market opportunity evaluation',
    industryInsights: 'Commercial space trends, defense spending patterns, technology transfer, international cooperation'
  },

  education: {
    type: 'Education Technology Specialist',
    experience: '8 years',
    domain: 'Education & Learning Systems',
    technicalSkills: 'Learning analytics, educational technology, curriculum design, assessment methods, digital pedagogy',
    domainKnowledge: 'Educational research, technology adoption, learning outcomes, institutional change, policy implementation',
    methodologicalApproach: 'Evidence-based education research, user-centered design, impact measurement, scalability assessment',
    industryInsights: 'EdTech market dynamics, institutional technology adoption, learning effectiveness research, policy trends'
  },

  government: {
    type: 'Public Policy Analyst',
    experience: '10 years',
    domain: 'Government & Public Policy',
    technicalSkills: 'Policy analysis, regulatory assessment, stakeholder analysis, impact evaluation, governance frameworks',
    domainKnowledge: 'Government operations, regulatory processes, public-private partnerships, policy implementation, civic technology',
    methodologicalApproach: 'Policy analysis frameworks, stakeholder mapping, impact assessment, implementation feasibility study',
    industryInsights: 'Government technology adoption, regulatory trends, public sector innovation, digital government initiatives'
  },

  general: {
    type: 'Strategic Research Analyst',
    experience: '10 years',
    domain: 'Cross-Industry Analysis',
    technicalSkills: 'Research methodology, data analysis, strategic planning, market research, competitive intelligence',
    domainKnowledge: 'Industry analysis, market dynamics, innovation patterns, business strategy, organizational behavior',
    methodologicalApproach: 'Systematic research approach, multi-perspective analysis, evidence-based insights, strategic synthesis',
    industryInsights: 'Cross-industry trends, innovation diffusion, market transformation patterns, strategic best practices'
  }
};

/**
 * Domain detection keywords and patterns
 */
const DOMAIN_KEYWORDS: Record<ResearchDomain, string[]> = {
  technology: [
    'AI', 'artificial intelligence', 'machine learning', 'ML', 'software', 'app', 'platform', 'cloud', 'SaaS',
    'cybersecurity', 'blockchain', 'cryptocurrency', 'IoT', 'internet of things', 'API', 'mobile', 'web',
    'data science', 'big data', 'analytics', 'algorithm', 'programming', 'development', 'tech stack',
    'DevOps', 'automation', 'robotics', 'quantum computing', 'edge computing', 'microservices'
  ],

  business: [
    'business model', 'strategy', 'marketing', 'sales', 'revenue', 'profit', 'growth', 'market share',
    'competitive advantage', 'customer acquisition', 'B2B', 'B2C', 'venture capital', 'startup',
    'IPO', 'merger', 'acquisition', 'franchise', 'consulting', 'operations', 'supply chain',
    'management', 'leadership', 'organizational', 'enterprise', 'corporate', 'commercial'
  ],

  science: [
    'research', 'study', 'experiment', 'clinical trial', 'peer review', 'publication', 'journal',
    'hypothesis', 'methodology', 'data analysis', 'statistical', 'laboratory', 'scientific method',
    'biology', 'chemistry', 'physics', 'medicine', 'pharmaceutical', 'biotech', 'life sciences',
    'academic', 'university', 'research institution', 'R&D', 'innovation', 'discovery'
  ],

  finance: [
    'investment', 'stock', 'bond', 'portfolio', 'fund', 'hedge fund', 'private equity', 'venture capital',
    'IPO', 'market', 'trading', 'financial', 'banking', 'credit', 'loan', 'mortgage', 'insurance',
    'valuation', 'asset', 'liability', 'cash flow', 'revenue', 'profit', 'loss', 'dividend',
    'inflation', 'interest rate', 'currency', 'forex', 'cryptocurrency', 'fintech'
  ],

  healthcare: [
    'healthcare', 'medical', 'hospital', 'clinic', 'doctor', 'patient', 'treatment', 'therapy',
    'drug', 'pharmaceutical', 'medication', 'clinical trial', 'FDA', 'medical device', 'diagnostics',
    'telemedicine', 'digital health', 'health tech', 'biotech', 'life sciences', 'wellness',
    'public health', 'epidemiology', 'vaccine', 'surgery', 'nursing', 'mental health'
  ],

  energy: [
    'energy', 'renewable', 'solar', 'wind', 'battery', 'electric', 'power', 'grid', 'utility',
    'oil', 'gas', 'coal', 'nuclear', 'carbon', 'emissions', 'climate', 'sustainability',
    'green', 'clean energy', 'fossil fuel', 'electricity', 'generator', 'turbine',
    'energy storage', 'smart grid', 'energy efficiency', 'environmental'
  ],

  automotive: [
    'automotive', 'car', 'vehicle', 'electric vehicle', 'EV', 'autonomous', 'self-driving',
    'Tesla', 'Ford', 'GM', 'Toyota', 'transportation', 'mobility', 'ride sharing',
    'automotive industry', 'manufacturing', 'supply chain', 'dealership', 'OEM',
    'battery', 'charging', 'hybrid', 'internal combustion', 'engine'
  ],

  aerospace: [
    'aerospace', 'aviation', 'aircraft', 'airplane', 'space', 'satellite', 'rocket', 'NASA',
    'SpaceX', 'Boeing', 'Airbus', 'defense', 'military', 'drone', 'UAV', 'flight',
    'airport', 'airline', 'pilot', 'aviation industry', 'commercial aviation',
    'space exploration', 'space technology', 'launch', 'orbit'
  ],

  education: [
    'education', 'school', 'university', 'college', 'student', 'teacher', 'learning',
    'curriculum', 'online learning', 'e-learning', 'EdTech', 'MOOC', 'classroom',
    'training', 'certification', 'degree', 'academic', 'pedagogy', 'educational technology',
    'K-12', 'higher education', 'distance learning', 'educational software'
  ],

  government: [
    'government', 'policy', 'regulation', 'regulatory', 'public sector', 'federal', 'state',
    'local government', 'municipal', 'congress', 'senate', 'legislature', 'law', 'legal',
    'compliance', 'governance', 'public administration', 'civic', 'political',
    'public policy', 'government contract', 'public service', 'taxpayer'
  ],

  general: []
};

/**
 * Expert selector with intelligent domain detection
 */
export class ExpertSelector {
  /**
   * Select appropriate expert role based on research query
   */
  static selectExpert(query: string, explicitDomain?: string): ExpertRole {
    try {
      const domain = (explicitDomain as ResearchDomain) || this.detectDomain(query);
      const expert = EXPERT_ROLES[domain];

      routerLogger.info('Expert role selected', {
        query: query.substring(0, 50),
        detectedDomain: domain,
        expertType: expert.type,
        experience: expert.experience
      });

      return expert;

    } catch (error) {
      routerLogger.error('Expert selection failed, using default', { error, query: query.substring(0, 50) });
      return EXPERT_ROLES.general;
    }
  }

  /**
   * Detect research domain from query text
   */
  static detectDomain(query: string): ResearchDomain {
    const queryLower = query.toLowerCase();
    const domainScores: Record<ResearchDomain, number> = {
      technology: 0,
      business: 0,
      science: 0,
      finance: 0,
      healthcare: 0,
      energy: 0,
      automotive: 0,
      aerospace: 0,
      education: 0,
      government: 0,
      general: 0
    };

    // Score each domain based on keyword matches
    for (const [domain, keywords] of Object.entries(DOMAIN_KEYWORDS)) {
      if (domain === 'general') continue;

      for (const keyword of keywords) {
        const keywordLower = keyword.toLowerCase();
        
        // Exact word match (higher score)
        const wordBoundaryRegex = new RegExp(`\\b${keywordLower}\\b`, 'g');
        const exactMatches = (queryLower.match(wordBoundaryRegex) || []).length;
        domainScores[domain as ResearchDomain] += exactMatches * 2;

        // Partial match (lower score)
        if (queryLower.includes(keywordLower)) {
          domainScores[domain as ResearchDomain] += 0.5;
        }
      }
    }

    // Find domain with highest score
    const sortedDomains = Object.entries(domainScores)
      .filter(([domain]) => domain !== 'general')
      .sort(([, a], [, b]) => b - a);

    const topDomain = sortedDomains[0];
    const secondDomain = sortedDomains[1];

    routerLogger.debug('Domain detection results', {
      query: query.substring(0, 50),
      topDomain: topDomain[0],
      topScore: topDomain[1],
      secondDomain: secondDomain[0],
      secondScore: secondDomain[1],
      allScores: domainScores
    });

    // Use top domain if it has a clear lead, otherwise use general
    if (topDomain[1] > 1 && topDomain[1] > secondDomain[1] * 1.5) {
      return topDomain[0] as ResearchDomain;
    } else {
      routerLogger.info('No clear domain detected, using general expert', {
        query: query.substring(0, 50),
        topScore: topDomain[1]
      });
      return 'general';
    }
  }

  /**
   * Get expert role for specific domain
   */
  static getExpertForDomain(domain: ResearchDomain): ExpertRole {
    return EXPERT_ROLES[domain] || EXPERT_ROLES.general;
  }

  /**
   * Get all available domains
   */
  static getAllDomains(): ResearchDomain[] {
    return Object.keys(EXPERT_ROLES) as ResearchDomain[];
  }

  /**
   * Validate expert role completeness
   */
  static validateExpert(expert: ExpertRole): boolean {
    const required = ['type', 'experience', 'domain', 'technicalSkills', 'domainKnowledge', 'methodologicalApproach', 'industryInsights'];
    
    for (const field of required) {
      if (!expert[field as keyof ExpertRole] || expert[field as keyof ExpertRole].length === 0) {
        routerLogger.error('Invalid expert role - missing field', { field, expert });
        return false;
      }
    }

    return true;
  }

  /**
   * Format expert role for prompt injection
   */
  static formatExpertForPrompt(expert: ExpertRole): Record<string, string> {
    return {
      expertType: expert.type,
      yearsExperience: expert.experience,
      domain: expert.domain,
      technicalSkills: expert.technicalSkills,
      domainKnowledge: expert.domainKnowledge,
      methodologicalApproach: expert.methodologicalApproach,
      industryInsights: expert.industryInsights
    };
  }

  /**
   * Get domain detection statistics for monitoring
   */
  static getDomainStats(query: string): {
    detectedDomain: ResearchDomain;
    confidence: number;
    alternativeDomains: Array<{ domain: ResearchDomain; score: number }>;
    keywordMatches: Record<ResearchDomain, string[]>;
  } {
    const queryLower = query.toLowerCase();
    const domainScores: Record<ResearchDomain, number> = {
      technology: 0, business: 0, science: 0, finance: 0, healthcare: 0,
      energy: 0, automotive: 0, aerospace: 0, education: 0, government: 0, general: 0
    };
    
    const keywordMatches: Record<ResearchDomain, string[]> = {
      technology: [], business: [], science: [], finance: [], healthcare: [],
      energy: [], automotive: [], aerospace: [], education: [], government: [], general: []
    };

    // Calculate scores and track matches
    for (const [domain, keywords] of Object.entries(DOMAIN_KEYWORDS)) {
      if (domain === 'general') continue;

      for (const keyword of keywords) {
        const keywordLower = keyword.toLowerCase();
        
        if (queryLower.includes(keywordLower)) {
          const wordBoundaryRegex = new RegExp(`\\b${keywordLower}\\b`, 'g');
          const exactMatches = (queryLower.match(wordBoundaryRegex) || []).length;
          
          if (exactMatches > 0) {
            domainScores[domain as ResearchDomain] += exactMatches * 2;
            keywordMatches[domain as ResearchDomain].push(keyword);
          } else {
            domainScores[domain as ResearchDomain] += 0.5;
          }
        }
      }
    }

    const sortedDomains = Object.entries(domainScores)
      .filter(([domain]) => domain !== 'general')
      .sort(([, a], [, b]) => b - a)
      .map(([domain, score]) => ({ domain: domain as ResearchDomain, score }));

    const topScore = sortedDomains[0]?.score || 0;
    const secondScore = sortedDomains[1]?.score || 0;
    
    const detectedDomain = topScore > 1 && topScore > secondScore * 1.5 
      ? sortedDomains[0].domain 
      : 'general';
    
    const confidence = topScore > 0 ? Math.min(topScore / 10, 1) : 0;

    return {
      detectedDomain,
      confidence,
      alternativeDomains: sortedDomains.slice(0, 3),
      keywordMatches
    };
  }
}